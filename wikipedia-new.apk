<!DOCTYPE html>
<html lang="en-US" class="auto-theme">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width" />
    <meta name="theme-color" content="#b4b4b4">
          <!-- InMobi Choice. Consent Manager Tag v3.0 (for TCF 2.2) -->
        <script type="text/javascript" async=true>
            var inmobi_host="apkmirror.com"
!function(){var e=inmobi_host,t=document.createElement("script"),n=document.getElementsByTagName("script")[0],e="https://cmp.inmobi.com".concat("/choice/","95hmrIiT9zWMo","/",e,"/choice.js?tag_version=V3"),a=0;t.async=!0,t.type="text/javascript",t.src=e,n.parentNode.insertBefore(t,n);for(var i,p="__tcfapiLocator",s=[],o=window;o;){try{if(o.frames[p]){i=o;break}}catch(e){}if(o===window.top)break;o=o.parent}i||(!function e(){var t,n=o.document,a=!!o.frames[p];return a||(n.body?((t=n.createElement("iframe")).style.cssText="display:none",t.name=p,n.body.appendChild(t)):setTimeout(e,5)),!a}(),o.__tcfapi=function(){var e,t=arguments;if(!t.length)return s;"setGdprApplies"===t[0]?3<t.length&&2===t[2]&&"boolean"==typeof t[3]&&(e=t[3],"function"==typeof t[2])&&t[2]("set",!0):"ping"===t[0]?"function"==typeof t[2]&&t[2]({gdprApplies:e,cmpLoaded:!1,cmpStatus:"stub"}):("init"===t[0]&&"object"==typeof t[3]&&(t[3]=Object.assign(t[3],{tag_version:"V3"})),s.push(t))},o.addEventListener("message",function(n){var a="string"==typeof n.data,e={};try{e=a?JSON.parse(n.data):n.data}catch(e){}var i=e.__tcfapiCall;i&&window.__tcfapi(i.command,i.version,function(e,t){e={__tcfapiReturn:{returnValue:e,success:t,callId:i.callId}};a&&(e=JSON.stringify(e)),n&&n.source&&n.source.postMessage&&n.source.postMessage(e,"*")},i.parameter)},!1));{const d=["2:tcfeuv2","6:uspv1","7:usnatv1","8:usca","9:usvav1","10:uscov1","11:usutv1","12:usctv1"];window.__gpp_addFrame=function(e){var t;window.frames[e]||(document.body?((t=document.createElement("iframe")).style.cssText="display:none",t.name=e,document.body.appendChild(t)):window.setTimeout(window.__gpp_addFrame,10,e))},window.__gpp_stub=function(){var e=arguments;if(__gpp.queue=__gpp.queue||[],__gpp.events=__gpp.events||[],!e.length||1==e.length&&"queue"==e[0])return __gpp.queue;if(1==e.length&&"events"==e[0])return __gpp.events;var t=e[0],n=1<e.length?e[1]:null,a=2<e.length?e[2]:null;if("ping"===t)n({gppVersion:"1.1",cmpStatus:"stub",cmpDisplayStatus:"hidden",signalStatus:"not ready",supportedAPIs:d,cmpId:10,sectionList:[],applicableSections:[-1],gppString:"",parsedSections:{}},!0);else if("addEventListener"===t){"lastId"in __gpp||(__gpp.lastId=0),__gpp.lastId++;var i=__gpp.lastId;__gpp.events.push({id:i,callback:n,parameter:a}),n({eventName:"listenerRegistered",listenerId:i,data:!0,pingData:{gppVersion:"1.1",cmpStatus:"stub",cmpDisplayStatus:"hidden",signalStatus:"not ready",supportedAPIs:d,cmpId:10,sectionList:[],applicableSections:[-1],gppString:"",parsedSections:{}}},!0)}else if("removeEventListener"===t){for(var p=!1,s=0;s<__gpp.events.length;s++)if(__gpp.events[s].id==a){__gpp.events.splice(s,1),p=!0;break}n({eventName:"listenerRemoved",listenerId:a,data:p,pingData:{gppVersion:"1.1",cmpStatus:"stub",cmpDisplayStatus:"hidden",signalStatus:"not ready",supportedAPIs:d,cmpId:10,sectionList:[],applicableSections:[-1],gppString:"",parsedSections:{}}},!0)}else"hasSection"===t?n(!1,!0):"getSection"===t||"getField"===t?n(null,!0):__gpp.queue.push([].slice.apply(e))},window.__gpp_msghandler=function(n){var a,i="string"==typeof n.data;try{var t=i?JSON.parse(n.data):n.data}catch(e){t=null}"object"==typeof t&&null!==t&&"__gppCall"in t&&(a=t.__gppCall,window.__gpp(a.command,function(e,t){e={__gppReturn:{returnValue:e,success:t,callId:a.callId}};n.source.postMessage(i?JSON.stringify(e):e,"*")},"parameter"in a?a.parameter:null,"version"in a?a.version:"1.1"))},"__gpp"in window&&"function"==typeof window.__gpp||(window.__gpp=window.__gpp_stub,window.addEventListener("message",window.__gpp_msghandler,!1),window.__gpp_addFrame("__gppLocator"))}function r(){var e=arguments;typeof window.__uspapi!==r&&setTimeout(function(){void 0!==window.__uspapi&&window.__uspapi.apply(window.__uspapi,e)},500)}var c;void 0===window.__uspapi&&(window.__uspapi=r,c=setInterval(function(){a++,window.__uspapi===r&&a<3?console.warn("USP is not accessible"):clearInterval(c)},6e3))}();var APKM=window.APKM||{};APKM.cmd=APKM.cmd||[],function(){window.__uspapi("getUSPData",1,function(e,t){t&&"1---"!==e.uspString&&APKM.cmd.push(function(){APKM.docReady(function(){document.querySelector("#apkm_consent_holder").insertAdjacentHTML("beforeend",'<a href="#" data-google-interstitial="false" onclick="window.__uspapi(\'displayUspUi\'); return false;">Do Not Sell My Personal Information | </a>')})})});var n=!1;window.__tcfapi("addEventListener",2,function(e,t){!t||"tcloaded"!==e.eventStatus&&"useractioncomplete"!==e.eventStatus||!e.gdprApplies||n||(n=!0,APKM.cmd.push(function(){APKM.docReady(function(){document.querySelector("#apkm_consent_holder").insertAdjacentHTML("beforeend",'<a href="#" data-google-interstitial="false" onclick="window.__tcfapi(\'displayConsentUi\', 2, (function() {})); return false;">Change consent | </a>')})}))})}();        </script>
        <!-- End InMobi Choice. Consent Manager Tag v3.0 (for TCF 2.2) -->
        <link rel="pingback" href="/wordpress/xmlrpc.php">
    <!--[if lt IE 9]>
    <script src="/wp-content/themes/APKMirror/js/html5.js"></script>
    <![endif]-->
    <meta name='robots' content='index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1' />
	<style>img:is([sizes="auto" i], [sizes^="auto," i]) { contain-intrinsic-size: 3000px 1500px }</style>
	    <script type="text/javascript">
        let AsyncGalleryUrl = 'https://www.apkmirror.com/wp-content/themes/APKMirror/js/async-gallery.min.js?1744932164';
        var ai_ignore_iframe_ids,APKM_debug=0,APKM_debug_log=[],APKM_isDocReady=!1;void 0!==ai_ignore_iframe_ids&&ai_ignore_iframe_ids.constructor===Array?ai_ignore_iframe_ids.push("dsq-app*"):ai_ignore_iframe_ids=["dsq-app*"];try{("ontouchstart"in window||"ontouch"in window)&&document.documentElement.classList.add("apkm-touch")}catch(e){}var APKM=function(){let l=/Android|webOS|iPhone|iPod|iPad|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);function d(e){return document.querySelectorAll(e)}function u(e){return d(e)[0]}function a(e){"complete"===document.readyState||"loading"!==document.readyState&&!document.documentElement.doScroll?e():document.addEventListener("DOMContentLoaded",e)}function i(e,t=2){return!!(e.offsetHeight>t||e.getClientRects().length>t)}function h(e,t,a){return new CustomEvent(e+".bs."+t,{cancelable:!0,bubbles:!0,relatedTarget:a})}function f(e){if(this)return this.dispatchEvent(e)}let g=function(){let o=document.createElement("div"),i=null;function n(e){var t,a;i&&s(i),document.body.dataset.originalPadding=parseInt(getComputedStyle(document.body).paddingRight,10),f.call(e,h("show","modal")),e.addEventListener("transitionend",()=>{f.call(e,h("shown","modal"))},{once:!0}),e.addEventListener("click",c,!1),document.body.appendChild(o),document.body.style.paddingRight=document.body.dataset.originalPadding+((t=document.createElement("div")).style.visibility="hidden",t.style.overflow="scroll",document.body.appendChild(t),a=document.createElement("div"),t.appendChild(a),a=t.offsetWidth-a.offsetWidth,t.parentNode.removeChild(t),a)+"px",document.body.classList.add("modal-open"),document.body.classList.add("apkm-modal-open"),e.classList.add("in"),e.offsetWidth,e.classList.add("shown"),e.setAttribute("aria-hidden",!1),i=e}function s(e){if(e)return!!f.call(e,h("hide","modal"))&&(i=null,e.removeEventListener("click",c,!1),document.body.style.paddingRight=document.body.dataset.originalPadding+"px",document.body.classList.remove("modal-open"),document.body.classList.remove("apkm-modal-open"),e.classList.remove("in"),e.classList.remove("shown"),e.setAttribute("aria-hidden",!0),o.remove(),!0)}function c(e){var t;e&&("hashchange"==e.type?i?s(i):a(e):"click"==e.type&&("modal"==(t=e.target).getAttribute("data-dismiss")||t.closest('[data-dismiss="modal"]')||t==i)&&(e.preventDefault(),s(i))&&history.back())}function a(e){var t,a=window.location.hash;a&&void 0!==(t=u(a))&&!t.classList.contains("modal-disable-hashloading")&&t&&t.classList.contains("modal")&&(e||(history.replaceState("",document.title,window.location.pathname+window.location.search),history.pushState("",document.title,window.location.pathname+window.location.search+a)),n(t))}return o.classList.add("modal-backdrop"),window.addEventListener("hashchange",c,!1),document.addEventListener("keydown",function(e){}),document.addEventListener("keydown",function(e){"input"!==e.target.tagName.toLowerCase()&&"textarea"!==e.target.tagName.toLowerCase()&&i&&27===e.which&&history.back()},!1),{hide:s,showFromHash:a,showFromAction:function(e,t){t&&t.preventDefault(),t=e.id||"APKModal",history.pushState("",document.title,window.location.pathname+window.location.search+"#"+t),n(e)}}}(),m=function(){let o=document.createElement("div"),t='[data-toggle="dropdown"]';function i(e){return u(this.dataset.target||!!this.getAttribute("href")&&this.getAttribute("href").replace(/.*(?=#[^\s]*$)/,""))||this.parentNode}function n(e){o.remove(),d(t).forEach(function(e){var t=i.call(e);t&&t.classList.contains("open")&&f.call(t,h("hide","dropdown"))&&(e.setAttribute("aria-expanded","false"),t.classList.remove("open"),f.call(t,h("hidden","dropdown")))})}return o.classList.add("dropdown-backdrop"),{toggle:function(e){if(void 0===this.classList||!this.classList.contains("disabled")&&!this.hasAttribute("disabled")){var t=i.call(this),a=t.classList.contains("open");if(n(),!a){if("ontouchstart"in document.documentElement&&!t.closest(".navbar-nav")&&this.parentNode.appendChild(o),!f.call(t,h("show","dropdown")))return;this.setAttribute("aria-expanded","true"),t.classList.add("open"),f.call(t,h("shown","dropdown"))}return!1}},clearDropdowns:n}}(),p=function(){let l={};function a(o){let i=this,n=i.closest("ul:not(.dropdown-menu)"),s=u(i.dataset.target||i.getAttribute("href").replace(/.*(?=#[^\s]*$)/,""));if(s&&("li"!==i.parentNode.tagName.toLowerCase()||!i.parentNode.classList.contains("active"))){n.classList.contains("sticky-tabs")&&history&&history.pushState&&window.location.hash!=s.id&&(!o||"hashchange"!=o.type)&&history.pushState(null,null,window.location.pathname+window.location.search+"#"+s.id);let e=n.querySelector(".active a"),t=f.call(e,h("hide","tab",i)),a=f.call(i,h("show","tab",e));if(t&&a&&(d(i.closest("li"),n),d(s,s.parentNode,function(){f.call(e,h("hidden","tab")),f.call(i,h("shown","tab"))}),"undefined"!=typeof tabadcode)&&!(s.id in l)&&s.querySelector(".gooWindow")){try{var c=document.createElement("div"),r=(c.innerHTML=tabadcode[s.id],c.querySelector("script"));let e="";r&&((e=document.createElement("script")).innerHTML=r.innerHTML,r.remove()),s.querySelector(".gooWindow").append(c),s.querySelector(".gooWindow").append(e)}catch(e){}l[s.id]=!0}}}function d(e,t,a){t=t.querySelector(":scope > .active");t.classList.remove("active"),t.querySelectorAll(":scope > .dropdown-menu > .active").forEach(function(e){e.classList.remove("active")}),t.querySelectorAll('[data-toggle="tab"]').forEach(function(e){e.setAttribute("aria-expanded","false")}),e.classList.add("active"),e.querySelectorAll('[data-toggle="tab"]').forEach(function(e){e.setAttribute("aria-expanded","true")}),e.closest(".dropdown-menu")&&(e.closest("li.dropdown").classList.add("active"),e.querySelectorAll('[data-toggle="tab"]').forEach(function(e){e.setAttribute("aria-expanded","true")})),a&&a()}function e(e){var t=window.location.hash,t=u('[data-toggle="tab"][href="'+t+'"],[data-toggle="pill"][href="'+t+'"]');t&&a.call(t,e)}return window.addEventListener("hashchange",e,!1),{show:a,showFromHash:e}}();function v(e=!1){let t=u("html"),a=localStorage.theme;if(e){switch(a){case"dark":a="light";break;case"light":a="";break;default:a="dark"}localStorage.theme=a}switch(a){case"dark":t.classList.add("dark"),t.classList.remove("auto-theme");break;case"light":t.classList.remove("dark"),t.classList.remove("auto-theme");break;default:window.matchMedia("(prefers-color-scheme: dark)").matches?t.classList.add("dark"):t.classList.remove("dark"),t.classList.add("auto-theme")}try{DISQUS.reset({reload:!0})}catch(e){}}function w(){var e=u(".advanced-search-container");e.classList.contains("hidden")?(e.classList.remove("hidden"),u("#advanced-searched-caret").classList.add("dropup")):(e.classList.add("hidden"),u("#advanced-searched-caret").classList.remove("dropup"))}function b(e,t=!1){"function"==typeof ga&&(ga("send",e),t=!0),"function"==typeof gtag&&(gtag("event",e.eventAction,{event_category:e.eventCategory,event_label:e.eventLabel}),t=!0),t||APKM_isDocReady||a(function(){b(e,!0)})}function o(e){e.removeAttribute("rows"),e.style.cssText="height: auto",e.style.cssText="height:"+e.scrollHeight+"px"}function n(t){for(let e=t.length;0<e;e--){var a=t[e-1]||!1;if(a&&a.getBoundingClientRect().top<50){var o=a.id||a.name,a=u(a.dataset.target);a&&(a.querySelectorAll("li").forEach(e=>{e.classList.remove("active")}),a.querySelector(`a[href$="#${o}"]`).closest("li").classList.add("active"));break}}}window.matchMedia&&(v(),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",e=>{localStorage.theme||v()})),document.addEventListener("input",function(e){"textarea"===e.target.tagName.toLowerCase()&&e.target.classList.contains("auto-size")&&o(e.target)}),document.addEventListener("click",function(e){var t=e.target,a=t.hasAttribute("data-apkm-click-track")?t:t.closest("[data-apkm-click-track]")||!1,a=(a&&b({hitType:"event",eventAction:"click",eventCategory:a.dataset.apkmTrackCategory??"APKM tracking",eventLabel:a.dataset.apkmClickTrack}),t.getAttribute("data-update-theme")?t:t.closest("[data-update-theme]")||!1),a=(a&&v(!0),t.classList.contains("infoLink")?t:t.closest(".infoLink")||!1);if(a)i=(a=a).closest(".appRow").nextElementSibling,o=i.classList.contains("activated"),d(".infoSlide").forEach(function(e){e.classList.remove("activated"),e.style.height=""}),d("svg.info-icon").forEach(function(e){e.style.color="",e.classList.remove("accent_color")}),o||(i.classList.add("activated"),i.style.height=i.scrollHeight+"px",a.querySelector(".info-icon").classList.add("accent_color"));else{var o=t.classList.contains("infoDevLink")?t:t.closest(".infoDevLink")||!1;if(o)a=(i=o).closest(".appRow").nextElementSibling,n=i.classList.contains("activated"),d(".infoDevSlide, .infoSlide, .infoDevLink").forEach(function(e){e.classList.remove("activated"),e.style.height=""}),n||(i.classList.add("activated"),a.style.height=a.scrollHeight+"px");else{var i,n=t.classList.contains("search-filter-button")?t:t.closest(".search-filter-button")||!1;if(n)u("#apkm-search-filters")?w():(u("#search-form").insertAdjacentHTML("beforeend",'<input type="hidden" name="filter" value="true" /> '),u("#apkm-search-searchtype").value="apk",document.forms["search-form"].submit());else if("apkm-advanced-search"==t.id?t:t.closest("#apkm-advanced-search")||!1)w();else{a=t.classList.contains("uploadButton")?t:t.closest(".uploadButton")||!1;if(a)a=a,l?location.href=a.dataset.url:g.showFromAction(u("#uploadAPK"));else{a="slide"==t.getAttribute("data-toggle")?t:t.closest('[data-toggle="slide"]')||!1;if(a)(r=a.hasAttribute("href")&&a.getAttribute("href")||a.querySelector("a")&&a.querySelector("a").hasAttribute("href")&&a.querySelector("a").getAttribute("href")||!1)&&history.replaceState&&(e.preventDefault(),window.location.hash?window.history.replaceState(null,null,"#"+r.split("#")[1]):window.history.pushState(null,null,"#"+r.split("#")[1])),a=(r=a).closest(".slide-container").querySelector(".slide-item"),s=r.classList.contains("activated"),c=void 0!==r.dataset.stayOpen,a&&(c||d('.slide-container .slide-item, [data-toggle="slide"]').forEach(function(e){e.classList.remove("activated"),e.style.height=""}),s?c&&(r.classList.remove("activated"),a.style.height=""):(r.classList.add("activated"),a.style.height=a.scrollHeight+"px"));else{var s="modal"==t.getAttribute("data-toggle")?t:t.closest('[data-toggle="modal"]')||!1;if(s){var c=s.dataset.target||!!s.getAttribute("href")&&s.getAttribute("href").replace(/.*(?=#[^\s]*$)/,"");if(c)return void g.showFromAction(u(c),e)}var r="dropdown"==t.getAttribute("data-toggle")?t:t.closest('[data-toggle="dropdown"]')||!1;r?m.toggle.call(r):(t.getAttribute("data-toggle")||m.clearDropdowns(),(a="tab"==t.getAttribute("data-toggle")||"pill"==t.getAttribute("data-toggle")?t:t.closest('[data-toggle="tab"]')||t.closest('[data-toggle="pill"]')||!1)?(e.preventDefault(),p.show.call(a)):(a="show-more"==t.getAttribute("data-toggle")?t:t.closest('[data-toggle="show-more"]')||!1)&&(e.preventDefault(),a.closest(".collapsable").classList.toggle("collapsed")))}}}}}},!1);let s=null,c=null;function r(){d('#content a[href$=".gif" i],#content a[href$=".jpg" i],#content a[href$=".jpeg" i],#content a[href$=".png" i],[data-lightbox]').forEach(e=>{let t="IMG"==e.nodeName?e:e.querySelector("img");t&&((e.href||e.dataset.href)&&t.setAttribute("data-large",e.href||e.dataset.href),t.classList.add("gallery__Image")),null===c&&e.addEventListener("click",async function(e){e.preventDefault(),async function(e,t){e.preventDefault(),null===s&&(await(s=APKM.loadJs(AsyncGalleryUrl)),c=new AsyncGallery,t.dispatchEvent(new Event("click")))}(e,t)})}),null!==c&&c.reInitItems()}let y=window.location.hash;a(function(){APKM_isDocReady=!0,y&&(p.showFromHash(),g.showFromHash(),t=u('[name="'+y.replace("#","")+'"]'))&&t.click();{const a=new IntersectionObserver(([e])=>{e.target.nextSibling.classList.toggle("sticked",0===e.intersectionRatio)},{threshold:[0,1]});document.querySelectorAll(".sticky-class, #sidebar > .ai_widget:first-of-type").forEach(e=>{var t;e&&(t=document.createElement("stickyobserver"),e.insertAdjacentElement("beforebegin",t),a.observe(t))})}let e=d('[data-spy="scroll"]');0<e.length&&(n(e),window.addEventListener("scroll",()=>{n(e)}));var t=u("#search-form"),t=(t&&t.addEventListener("submit",function(e){if(Math.max(document.documentElement.clientWidth||0,window.innerWidth||0)<992){var t=u(".searchbox-parent");if(!t.classList.contains("open"))return e.preventDefault(),t.classList.add("open"),t.querySelector(".searchbox").focus(),!1}if(u("#apkm-search-filters"))return e.preventDefault(),u("#apply-search-form").click(),!1}),d(".apkm-timed-slider")),t=(t&&t.forEach(function(a){let o=a.id||"apkm-timed-slider",e=parseInt(localStorage.getItem(o)),t=(new Date).getTime()/1e3,i=parseInt(a.getAttribute("data-ttl")||2592e3);var n;(!e||e+i<t)&&(n=a.querySelectorAll(".close, a"),a.classList.remove("hidden"),a.classList.add("show"),n.forEach(function(t){t.addEventListener("click",function(e){t.classList.contains("close")&&e.preventDefault(),localStorage.setItem(o,(new Date).getTime()/1e3),a.remove()})}))}),d("textarea.auto-size"));t&&t.forEach(e=>{o(e)}),r()});var e=void 0!==window.APKM&&window.APKM.cmd||[];if(0<e.length){for(var t=0;t<e.length;t++)"function"==typeof e[t]&&e[t]();e=[]}return{cmd:{push:function(e){e()}},docReady:a,rT:function(e,t){let a=Math.floor(Date.now()/1e3),o,i=setInterval(function(){0==(o=t-Math.floor(Math.floor(Date.now()/1e3)-a))||o<0?(e(0),clearInterval(i)):e(o)},1e3)},visible:i,vE:function(e,a){let o=0;return d(e).forEach(function(e,t){i(e,a)&&(o+=1)}),o},loadJs:function(i,n=!1){return new Promise(function(e,t){let a=document,o=a.createElement("script");o.src=i,0!=n&&(o.id=n),o.onerror=function(e){t(e,o)},o.onload=function(){e(!0)},(a.head||a.body).appendChild(o)})},modalShowFromAction:g.showFromAction,qs:u,qa:d,isMobile:l,setCookie:function(e,t,a){var o=new Date,a=(o.setTime(o.getTime()+24*a*60*60*1e3),"expires="+o.toUTCString());document.cookie=e+"="+t+"; "+a+";path=/"},getCookie:function(e){for(var t=e+"=",a=document.cookie.split(";"),o=0;o<a.length;o++){for(var i=a[o];" "==i.charAt(0);)i=i.substring(1);if(-1!=i.indexOf(t))return i.substring(t.length,i.length)}return""},a:function(e=0,t=!1){(t=t&&u(".ains-"+t+" .adslot"))&&!t.classList.contains("apkm-initialized")&&(t.classList.add("apkm-initialized"),(adsbygoogle=window.adsbygoogle||[]).push({}))},g:b,attachLightbox:r,aid:[],log:window.APKM_debug?console.log:function(e){APKM_debug_log.push(arguments)}}}();    </script>

	<!-- This site is optimized with the Yoast SEO Premium plugin v17.1 (Yoast SEO v23.9) - https://yoast.com/wordpress/plugins/seo/ -->
	<title>Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+) APK Download by Wikimedia Foundation - APKMirror</title>
	<meta name="description" content="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+) APK Download by Wikimedia Foundation - APKMirror Free and safe Android APK downloads" />
	<link rel="canonical" href="https://www.apkmirror.com/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/" />
	<meta property="og:locale" content="en_US" />
	<meta property="og:type" content="article" />
	<meta property="og:title" content="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+) APK Download by Wikimedia Foundation - APKMirror" />
	<meta property="og:url" content="https://www.apkmirror.com/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/" />
	<meta property="og:site_name" content="APKMirror" />
	<meta property="article:publisher" content="https://www.facebook.com/apkmirror" />
	<meta property="og:image" content="https://downloadr2.apkmirror.com/wp-content/uploads/2019/10/665878e0603d5_org.wikipedia-384x384.png" />
	<meta property="og:image:width" content="384" />
	<meta property="og:image:height" content="384" />
	<meta property="og:image:type" content="image/png" />
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:site" content="@APKMirror" />
	<script type="application/ld+json" class="yoast-schema-graph">{
	    "@context": "https://schema.org",
	    "@graph": [
	        {
	            "@type": "WebPage",
	            "@id": "https://www.apkmirror.com/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/",
	            "url": "https://www.apkmirror.com/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/",
	            "name": "Download Wikipedia 2.7.50538-samsung-2025-06-24 APK for Android - APKMirror",
	            "isPartOf": {
	                "@id": "https://www.apkmirror.com/#website"
	            },
	            "primaryImageOfPage": {
	                "@id": "https://www.apkmirror.com/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/#primaryimage"
	            },
	            "image": {
	                "@id": "https://www.apkmirror.com/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/#primaryimage"
	            },
	            "thumbnailUrl": "https://downloadr2.apkmirror.com/wp-content/uploads/2019/10/665878e0603d5_org.wikipedia.png",
	            "datePublished": "2025-07-08T03:47:24+00:00",
	            "dateModified": "2025-07-08T03:47:24+00:00",
	            "breadcrumb": {
	                "@id": "https://www.apkmirror.com/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/#breadcrumb"
	            },
	            "inLanguage": "en-US",
	            "potentialAction": [
	                {
	                    "@type": "ReadAction",
	                    "target": [
	                        "https://www.apkmirror.com/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/"
	                    ]
	                }
	            ]
	        },
	        {
	            "@type": "ImageObject",
	            "inLanguage": "en-US",
	            "@id": "https://www.apkmirror.com/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/#primaryimage",
	            "url": "https://downloadr2.apkmirror.com/wp-content/uploads/2019/10/665878e0603d5_org.wikipedia.png",
	            "contentUrl": "https://downloadr2.apkmirror.com/wp-content/uploads/2019/10/665878e0603d5_org.wikipedia.png",
	            "width": 512,
	            "height": 512
	        },
	        {
	            "@type": "BreadcrumbList",
	            "@id": "https://www.apkmirror.com/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/#breadcrumb",
	            "itemListElement": [
	                {
	                    "@type": "ListItem",
	                    "position": 1,
	                    "name": "Home",
	                    "item": "https://www.apkmirror.com/"
	                },
	                {
	                    "@type": "ListItem",
	                    "position": 2,
	                    "name": "Wikimedia Foundation",
	                    "item": "https://www.apkmirror.com/apk/wikimedia-foundation/"
	                },
	                {
	                    "@type": "ListItem",
	                    "position": 3,
	                    "name": "Wikipedia",
	                    "item": "https://www.apkmirror.com/apk/wikimedia-foundation/wikipedia/"
	                },
	                {
	                    "@type": "ListItem",
	                    "position": 4,
	                    "name": "Wikipedia 2.7.50538",
	                    "item": "https://www.apkmirror.com/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/"
	                },
	                {
	                    "@type": "ListItem",
	                    "position": 5,
	                    "name": "Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)"
	                }
	            ]
	        },
	        {
	            "@type": "WebSite",
	            "@id": "https://www.apkmirror.com/#website",
	            "url": "https://www.apkmirror.com/",
	            "name": "APKMirror",
	            "description": "Free and safe Android APK downloads",
	            "publisher": {
	                "@id": "https://www.apkmirror.com/#organization"
	            },
	            "potentialAction": [
	                {
	                    "@type": "SearchAction",
	                    "target": {
	                        "@type": "EntryPoint",
	                        "urlTemplate": "https://www.apkmirror.com/?s={search_term_string}"
	                    },
	                    "query-input": {
	                        "@type": "PropertyValueSpecification",
	                        "valueRequired": true,
	                        "valueName": "search_term_string"
	                    }
	                }
	            ],
	            "inLanguage": "en-US"
	        },
	        {
	            "@type": "Organization",
	            "@id": "https://www.apkmirror.com/#organization",
	            "name": "APKMirror",
	            "url": "https://www.apkmirror.com/",
	            "logo": {
	                "@type": "ImageObject",
	                "inLanguage": "en-US",
	                "@id": "https://www.apkmirror.com/#/schema/logo/image/",
	                "url": "",
	                "contentUrl": "",
	                "caption": "APKMirror"
	            },
	            "image": {
	                "@id": "https://www.apkmirror.com/#/schema/logo/image/"
	            },
	            "sameAs": [
	                "https://www.facebook.com/apkmirror",
	                "https://x.com/APKMirror"
	            ]
	        }
	    ]
	}</script>
	<!-- / Yoast SEO Premium plugin. -->


<link rel='dns-prefetch' href='//www.googletagmanager.com' />
<link rel='dns-prefetch' href='//stats.wp.com' />
<link rel='dns-prefetch' href='//fonts.googleapis.com' />
<style id='classic-theme-styles-inline-css' type='text/css'>
/*! This file is auto-generated */
.wp-block-button__link{color:#fff;background-color:#32373c;border-radius:9999px;box-shadow:none;text-decoration:none;padding:calc(.667em + 2px) calc(1.333em + 2px);font-size:1.125em}.wp-block-file__button{background:#32373c;color:#fff;text-decoration:none}
</style>
<link rel='stylesheet' id='APKMirror-style-css' href='https://www.apkmirror.com/wp-content/themes/APKMirror/style.min.css?ver=1749491838' type='text/css' media='all' />
<link rel='preload' as='style' onload="this.onload=null;this.rel='stylesheet'" id='googleFonts-async-css' href='//fonts.googleapis.com/css?family=Roboto%3A100%2C300%2C400%2C500&#038;display=swap&#038;ver=6.8.1' type='text/css' media='all' />

<!-- Google tag (gtag.js) snippet added by Site Kit -->

<!-- Google Analytics snippet added by Site Kit -->
<script type="text/javascript" src="https://www.googletagmanager.com/gtag/js?id=G-YYKGHJGKP4" id="google_gtagjs-js" async></script>
<script type="text/javascript" id="google_gtagjs-js-after">
/* <![CDATA[ */
window.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}
gtag("set","linker",{"domains":["www.apkmirror.com","memberful.com"]});
gtag("js", new Date());
gtag("set", "developer_id.dZTNiMT", true);
gtag("config", "G-YYKGHJGKP4");
/* ]]> */
</script>

<!-- End Google tag (gtag.js) snippet added by Site Kit -->
<link rel="EditURI" type="application/rsd+xml" title="RSD" href="/wordpress/xmlrpc.php?rsd" />
<meta name="generator" content="WordPress 6.8.1" />
<link rel='shortlink' href='/?p=9899167' />
<link rel="alternate" title="oEmbed (JSON)" type="application/json+oembed" href="https://www.apkmirror.com/wp-json/oembed/1.0/embed?url=%2Fapk%2Fwikimedia-foundation%2Fwikipedia%2Fwikipedia-2-7-50538-release%2Fwikipedia-2-7-50538-samsung-2025-06-24-android-apk-download%2F" />
<link rel="alternate" title="oEmbed (XML)" type="text/xml+oembed" href="https://www.apkmirror.com/wp-json/oembed/1.0/embed?url=%2Fapk%2Fwikimedia-foundation%2Fwikipedia%2Fwikipedia-2-7-50538-release%2Fwikipedia-2-7-50538-samsung-2025-06-24-android-apk-download%2F&#038;format=xml" />
<script>var apkm_disqus_shortname = "apkmirror";</script>    <script type="text/javascript">
        var ajaxurl = '/wordpress/wp-admin/admin-ajax.php';
    </script>
    <meta name="generator" content="Site Kit by Google 1.150.0" />
<!-- Google AdSense meta tags added by Site Kit -->
<meta name="google-adsense-platform-account" content="ca-host-pub-****************">
<meta name="google-adsense-platform-domain" content="sitekit.withgoogle.com">
<!-- End Google AdSense meta tags added by Site Kit -->
<style>
.ai-viewports                 {--ai: 1;}
.ai-viewport-6, .ai-viewport-6 .adsbygoogle, .ai-viewport-6 .adslot                { display: none !important;}
.ai-viewport-5, .ai-viewport-5 .adsbygoogle, .ai-viewport-5 .adslot                { display: none !important;}
.ai-viewport-1, .ai-viewport-1 .adsbygoogle, .ai-viewport-1 .adslot                { display: inherit !important;}
.ai-viewport-0, .ai-viewport-0 .adsbygoogle, .ai-viewport-0 .adslot                { display: none !important;}
@media (min-width: 481px) and (max-width: 992px) {
.ai-viewport-1, .ai-viewport-1 .adsbygoogle, .ai-viewport-1 .adslot                { display: none !important;}
.ai-viewport-5, .ai-viewport-5 .adsbygoogle, .ai-viewport-5 .adslot                { display: inherit !important;}
}
@media (max-width: 480px) {
.ai-viewport-1, .ai-viewport-1 .adsbygoogle, .ai-viewport-1 .adslot                { display: none !important;}
.ai-viewport-6, .ai-viewport-6 .adsbygoogle, .ai-viewport-6 .adslot                { display: inherit !important;}
}
</style>
<style>
.listWidget .appRow .table-row > div:last-child .downloadIconPositioning{
    display: none;
}
.listWidget .appRow .table-row > div:last-child .infoIconPositioning{
    float: right
}
 
#sidebar .listWidget .appRow .table-row > div:last-child{
    display: none;
}
 
#sidebar .listWidget .appRow .table-row .table-cell{
    height: 48px;
}

.variants-table .table-row > div.table-cell.addseparator:last-child{
    display: none;
}
</style>


<script>
  APKM.loadJs('//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js');
    
  (adsbygoogle = window.adsbygoogle || []).push({
      google_ad_client: 'ca-pub-8776668743582988',
      enable_page_level_ads: true,
      interstitials: {google_ad_channel: '4713454888'}
  });
</script>    <link rel="alternate" type="application/rss+xml" title="APKMirror Wikipedia App Feed" href="/apk/wikimedia-foundation/wikipedia/feed/" />
<link rel="alternate" type="application/rss+xml" title="APKMirror Wikimedia Foundation Dev Feed" href="/apk/wikimedia-foundation/feed/" />
    <link rel="alternate" type="application/rss+xml" title="APKMirror Main Feed" href="https://www.apkmirror.com/feed/" />
    <link rel="manifest" href="https://www.apkmirror.com/wp-content/themes/APKMirror/manifest.json?cachebust=4" />
    <link rel="shortcut icon" href="https://www.apkmirror.com/wp-content/themes/APKMirror/images/favicon.ico?v=2" />
    <link href="https://www.apkmirror.com/wp-content/themes/APKMirror/images/favicon.png?v=2" rel="icon" sizes="64x64">
    <link href="https://www.apkmirror.com/wp-content/themes/APKMirror/images/apple-touch-icon-180x180.png" rel="icon" sizes="192x192">

    <link rel="apple-touch-icon" href="https://www.apkmirror.com/wp-content/themes/APKMirror/images/apple-touch-icon.png">
    <link rel="apple-touch-icon" sizes="57x57" href="https://www.apkmirror.com/wp-content/themes/APKMirror/images/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="72x72" href="https://www.apkmirror.com/wp-content/themes/APKMirror/images/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="https://www.apkmirror.com/wp-content/themes/APKMirror/images/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="https://www.apkmirror.com/wp-content/themes/APKMirror/images/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="https://www.apkmirror.com/wp-content/themes/APKMirror/images/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="https://www.apkmirror.com/wp-content/themes/APKMirror/images/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="https://www.apkmirror.com/wp-content/themes/APKMirror/images/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="https://www.apkmirror.com/wp-content/themes/APKMirror/images/apple-touch-icon-180x180.png">

    <style type="text/css" media="screen">
        .notes a,
        .notes a:link,
        .notes a:visited,
        .notes a:hover,
        .notes a:active,

        .entry-content a:not(.btn),
        .entry-content a:not(.btn):link,
        .entry-content a:not(.btn):visited,
        .entry-content a:not(.btn):hover,
        .entry-content a:not(.btn):active,

        .accent_color,
        .accent_color:visited,
        .accent_color:hover,
        .accent_color:active,
        .accent_color:focus {
            color: #b4b4b4 !important;
        }

        .accent_bg {
            background-color: #b4b4b4 !important;
        }

        .accent_border {
            border-color: #b4b4b4 !important;
        }

            </style>

    
    <!-- Flattr -->
    <meta name="flattr:id" content="j099pg">
    <!-- END Flattr -->

    <!-- Coil -->
    <meta name="monetization" content="$ilp.uphold.com/QYAfJw3AKBgA">
    <!-- End Coil -->

    </head>

<body class="wp-singular apps_post-template-default single single-apps_post postid-9899167 wp-theme-APKMirror" role="document" >
        <div id="wrap">
        <div id="masthead" class="header">
            <div role="banner" class="accent_bg site-header-contents header-nav">
                <div class="container-fluid set-max-width">
                                        <!-- Main toolbar - for both desktop and mobile -->
                    <!-- Mobile hamburger menu-->

                    <div class="d-flex site-toolbar p-relative">
                        <div class="header-nav-menu f-grow col-padding header-alternate">
                                                        <div id="mobile-slide-menu">
                                <div id="mobile-slide-header" class="accent_bg site-info hidden-lg hidden-md d-flex f-start col-padding">
                                    <a class="fontWhite mobile-slide-logo noHover" href="https://www.apkmirror.com/">
                                        <img style="width:50px; height: 26px" width="50" height="26" src="https://www.apkmirror.com/wp-content/themes/APKMirror/images/logov2.png" class="header-image apkm-logo-image" alt="" />
                                    </a>
                                    <a class="slide-site-title fontWhite noHover" href="https://www.apkmirror.com/" title="APKMirror" rel="home">
                                        APKMirror                                     </a>
                                </div>
                                <nav class="navbar navbar-default" role="navigation">
                                    <div id="navbar-mobile" class="menu-nav-bar-container"><ul id="menu-nav-bar" class="nav navbar-nav"><li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2127"><a title="All Developers" href="/developers/">All Developers</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2055695"><a title="All Categories" href="/categories/">All Categories</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-61273"><a title="FAQ" href="/faq/">FAQ</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-61275"><a title="Contact" href="/contact-us/">Contact</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-3325449"><a title="Premium" href="/premium/">Premium</a></li>
</ul></div>                                </nav>
                            </div>
                        </div>
                        <div class="site-info d-flex col-padding ">
                            <button type="button" id="hamburgerMenu" class="hamburgerButton hidden-lg hidden-md" data-toggle="dropdown" data-target="#mobile-slide-menu">
                                <svg class="clickable icon hamburger-icon">
                                    <use xlink:href="#apkm-icon-hamburger"></use>
                                </svg>
                            </button>
                            <a class="fontWhite noHover visible-lg-inline visible-md-inline" href="https://www.apkmirror.com/">
                                <img class="apkm-logo-image" width="50" height="26" src="https://www.apkmirror.com/wp-content/themes/APKMirror/images/logov2.png" alt="" />
                            </a>
                            <a class="fontWhite noHover" href="https://www.apkmirror.com/" title="APKMirror" rel="home">
                                APKMirror                             </a>
                        </div>
                        <div class="search-bar d-flex f-nowrap f-end f-grow col-padding">
                            <!-- Search Form -->
                            <div class="searchbox-parent accent_bg">
                                <form action="/" method="get" id="search-form">
                                    <input type="hidden" id="apkm-search-post_type" name="post_type" value="app_release"><input type="hidden" id="apkm-search-searchtype" name="searchtype" value="apk"><input type="hidden" id="apkm-search-bundles" name="bundles[]" value="apkm_bundles"><input type="hidden" id="apkm-search-bundles" name="bundles[]" value="apk_files">                                    <button type="submit" class="searchButton" onclick="APKM_isDocReady || event.preventDefault()">
                                        <svg class="clickable icon search-icon">
                                            <use xlink:href="#apkm-icon-search"></use>
                                        </svg>
                                    </button>
                                    <input type="text" name="s" placeholder="Search" value="" class="searchbox" />
                                    <button type="button" class="search-filter-button">
                                        <svg class="clickable icon search-icon">
                                            <use xlink:href="#apkm-icon-searchfilter"></use>
                                        </svg>
                                    </button>
                                    <button type="button" class="backButton hidden-lg hidden-md" onclick="document.querySelector('.searchbox-parent').classList.remove('open')">
                                        <span class="clickable icon cancel-search-icon">×</span>
                                    </button>
                                </form>
                            </div>
                            <div class="site-actions d-flex f-nowrap">
                                <button type="button" class="uploadButton" data-url="https://www.apkmirror.com/apk-upload/">
                                    <svg class="clickable icon upload-icon">
                                        <use xlink:href="#apkm-icon-upload"></use>
                                    </svg>
                                </button>
                                                                                                    <div class="dropdown dropdown-premium button">
                                        <span class="dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                                                                            <svg class="clickable icon icon-user">
                                                    <use xlink:href="#apkm-icon-user"></use>
                                                </svg>
                                                                                    </span>
                                        <ul class="dropdown-menu dropdown-menu-right container-fluid set-max-width">
                                            <li><a href="https://www.apkmirror.com/premium" class="v-middle"><svg class="clickable icon apkm-premium"><use xlink:href="#apkm-checkmark"></use></svg> <span>APKMirror Premium</span></a></li><li><a href="https://www.apkmirror.com/?memberful_endpoint=auth" class="v-middle"><svg class="clickable icon"><use xlink:href="#apkm-login"></use></svg> <span>Sign in to Premium</span></a></li>                                        </ul>
                                    </div>
                                                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <header class="site-header-contents">
    <div class="container-fluid set-max-width">
        <!-- Desktop and Mobile alternative Header -->
                    <div class="siteTitleBar d-flex f-nowrap">
                <div class="p-relative icon-container"><img class="ellipsisText" style="width:96px; height:96px;" alt="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)"  id="primaryimage" src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=96&h=96&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=384&h=384&q=100 384w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=192&h=192&q=100 192w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=96&h=96&q=100 96w" sizes="96px"  loading="lazy"><span class="bubble "><a href="#disqus_thread" data-disqus-identifier="9899167 https://www.apkmirror.com/?post_type=apps_post&#038;p=9899167" title="View comments"></a></span></div>                <div class="f-grow">
                    <h1 title="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)" class="marginZero wrapText app-title fontBlack noHover">Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)</h1><h3 title="By Wikimedia Foundation" class="marginZero dev-title wrapText fontBlack">By <a href="/apk/wikimedia-foundation">Wikimedia Foundation</a></h3>                </div>
            </div>
                <nav class="navbar navbar-default col-padding f-100" role="navigation"><div class="accent_color" id="breadcrumbs"><a class="withoutripple accent_color" href="/apk/wikimedia-foundation/">Wikimedia Foundation</a>   <svg class="icon chevron-icon"><use xlink:href="#apkm-icon-chevron"></use></svg> <span class="dropdown breadcrumbs-menu"><span id="appsDD" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="withoutripple dropdown-message  accent_color">Wikipedia <span class="caret"></span></span><ul class="dropdown-menu breadcrumbs-menu" aria-labelledby="appsDD"><li><a class="accent_color" href="/apk/wikimedia-foundation/wikipedia/"><strong>Wikipedia </strong></a></li><li><a class="accent_color" href="/apk/wikimedia-foundation/wikipedia-fdroid-version/">Wikipedia (f-droid version) </a></li></ul></span>  <svg class="icon chevron-icon"><use xlink:href="#apkm-icon-chevron"></use></svg> <a class="withoutripple accent_color" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/">2.7.50538</a>   <svg class="icon chevron-icon"><use xlink:href="#apkm-icon-chevron"></use></svg> <span class="dropdown breadcrumbs-menu"><span id="variantsDD" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="dropdown-message active accent_color">(universal) (nodpi) (Android 5.0+) <span class="apkm-badge">APK</span>   <span class="caret"></span></span><ul class="dropdown-menu breadcrumbs-menu addsmallpadding" aria-labelledby="variantsDD"><li class="show-overflow"><div style="display: inline-block"><a class="accent_color" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-r-2025-06-24-android-apk-download/">(universal) (120-640dpi) (Android 5.0+)</a> <span class="apkm-badge success" data-apkm-tooltip="APK bundle with base APK and 11 splits">BUNDLE</span>  <span class="apkm-badge success" data-apkm-tooltip="11 splits">11 S</span>    </div></li><li class="show-overflow"><div style="display: inline-block"><a class="accent_color" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/"><strong>(universal) (nodpi) (Android 5.0+)</strong></a> <span class="apkm-badge">APK</span>   </div></li></ul></span> </div></nav>    </div>
</header>
        </div>
            </div>

    
    <script type="text/javascript">
        /**formData.append("_wpnonce", 'd70582c24c');
    */
        let apk_upload_page = false,
            apk_maxFiles = 25,
            apk_maxFilesSize = 5000,
            apk_upload_wpnonce = '',
            apk_tinymceUrl = '/wordpress/wp-includes/js/tinymce/tinymce.min.js?ver=6.8.1',
            apk_apkuploaderJS = 'https://www.apkmirror.com/wp-content/plugins/UploadManager//inc/js/apkuploader.min.js?1737067368',
            apk_md5Url = 'https://www.apkmirror.com/wp-content/plugins/UploadManager//inc/js/md5.umd.min.js?1709305723',
            apk_touchDragUrl = 'https://www.apkmirror.com/wp-content/plugins/UploadManager//inc/js/DragDropTouch.min.js?1697817296';
        var APKUploader=function(){let r,a=document.body,t=!1,e=!1,d=!1,s=!1;async function i(){e||(r=APKM.loadJs(apk_apkuploaderJS),e=!0)}async function n(e){s||d||(e=e.dataTransfer).types&&(e.types.indexOf?-1!=e.types.indexOf("Files"):e.types.contains("Files"))&&(i(),apk_upload_page||((t=t||document.querySelector("#uploadAPK"))||APKM.docReady(function(){(t=t||document.querySelector("#uploadAPK"))&&!t.classList.contains("in")&&APKM.modalShowFromAction(t)}),t&&!t.classList.contains("in")&&APKM.modalShowFromAction(t)),a.classList.add("dz-drag-hover"),d=!0)}function l(e){s||(a.classList.remove("dz-drag-hover"),d=!1)}function o(e){e.preventDefault(),e.stopPropagation()}return apk_upload_page?i():APKM.docReady(function(){(t=t||document.querySelector("#uploadAPK")).classList.contains("in")?i():t.addEventListener("show.bs.modal",function(){i()},{once:!0})}),APKM.isMobile||(["dragenter","dragover","dragleave","drop"].forEach(e=>{a.addEventListener(e,o,!1)}),["dragenter","dragover"].forEach(e=>{a.addEventListener(e,n,!1)}),a.addEventListener("dragleave",l,!1),a.addEventListener("drop",async function(e){if(!s&&d){l(),i(),await r;var a=e.dataTransfer;if(a&&a.items)try{var t=a.items;for(let e=0;e<t.length;e++){var n=t[e],o=n.webkitGetAsEntry();o.isDirectory?APKUploader.addDirectory(o):o.isFile&&null!=n.getAsFile&&APKUploader.addFile(n.getAsFile())}return}catch(e){console.error&&console.error(e,e.stack)}a.files&&APKUploader.handleFiles(e.dataTransfer.files)}},!1)),{setDisabled:function(e){s=e}}}();    </script>
    <div id="main" class="content">
        <div class="container-fluid set-max-width d-flex f-a-start">
            
<div id="content" class="f-md-66 col-padding content-area" role="main">
    <script>
var tabadcode = {};
</script>
<article id="post-9899167">
    <div class='ains ains-13 GEo  ains-has-label ains-apkm_apk_page_top ai-track' data-ai='WzEzLDEsImFwa21fYXBrX3BhZ2VfdG9wIiwiYXBrbV9nb29nbGVfYWRzIiwxXQ=='>
<span class="advertisement-text">Advertisement</span><a href="/premium" class="advertisement-text membership-text" title="APKMirror Premium" data-google-vignette="false"><span>Remove ads, dark theme, and more with <img src="/wp-content/themes/APKMirror/images/apple-touch-icon-57x57.png" alt="APKM Premium" width="18" height="18" /> Premium</span></a>
<div class="gooWidget yen google-ad-leaderboard">
  <!-- Adsense responsive ad apkm_apk_page_top -->
  <ins class="adsbygoogle adslot NJx"
    style="display:block"
    data-ad-client="ca-pub-8776668743582988"
    data-ad-slot="4921302080"
    data-ad-format="auto"
    data-full-width-responsive="true"></ins>
  <script>
    APKM.a(114, '13');
  </script>
</div></div>
    <!-- Nav tabs -->
            <div class="tab-container scroll-spy-container">
                <div class="tab-scrollable-container">
                    <ul class="nav nav-tabs sticky-tabs" role="tablist"><li  class="active"><a class="accent_border accent_color smooth-scroll" aria-controls="file" role="tab" href="#file" >DOWNLOAD</a></li><li ><a class="accent_border accent_color smooth-scroll" aria-controls="notes" role="tab" href="#notes" >NOTES</a></li><li ><a class="accent_border accent_color smooth-scroll" aria-controls="whatsnew" role="tab" href="#whatsnew" >WHAT'S NEW</a></li><li ><a class="accent_border accent_color smooth-scroll" aria-controls="description" role="tab" href="#description" >DESCRIPTION</a></li><li ><a class="accent_border accent_color smooth-scroll" aria-controls="gallery" role="tab" href="#gallery" >GALLERY</a></li><li ><a class="accent_border accent_color smooth-scroll" aria-controls="variants" role="tab" href="#variants" >ALL VARIANTS</a></li><li ><a class="accent_border accent_color smooth-scroll" href="#previous_apks">PREVIOUS APKS</a></li><li ><a class="accent_border accent_color smooth-scroll" href="#all_releases">ALL VERSIONS</a></li><li ><a class="accent_border accent_color smooth-scroll" href="#comments">COMMENTS</a></li></ul></div></div><div class="card-with-tabs unroll">    <div class="tab-buttons d-flex f-nowrap unroll">
        <div class="tab-button-positioning-left">
                            <a class="accent_color accent_border play-category" href="/categories/books_and_reference/">Books & Reference</a>
                    </div>

                <div class="tab-button-positioning d-flex f-nowrap">
            <div class="dropdown">
                <a role="button" title="View QR code" alt="View QR code" class="accent_color hidden-xs tab-button dropdown-toggle" id="qrDropdown" data-url="https://www.apkmirror.com/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/" data-toggle="dropdown" aria-haspopup="true" data-action="frontend_ajax_generate_qr" aria-expanded="false">
                    <svg class="icon qrcode-icon">
                        <use xlink:href="#apkm-icon-qrcode"></use>
                    </svg>
                </a>
                <ul class="dropdown-menu dropdown-menu-right" id="qrDropdownPanel" aria-labelledby="qrDropdown">
                    <li>
                        <div class="showbox">
                            <div class="loader"><svg class="circular" viewBox="25 25 50 50">
                                    <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10" />
                                </svg></div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="dropdown">
                <a role="button" title="Share on social media" alt="Share on social media" class="accent_color tab-button dropdown-toggle" id="shareDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <svg class="icon share-icon">
                        <use xlink:href="#apkm-icon-share"></use>
                    </svg>
                </a>
                <ul class="dropdown-menu dropdown-menu-right" id="shareDropdownPanel" aria-labelledby="shareDropdown">
                                        <li>
                        <div class="share_fallback">
                            <a href="https://www.facebook.com/sharer.php?u=https%3A%2F%2Fwww.apkmirror.com%2Fapk%2Fwikimedia-foundation%2Fwikipedia%2Fwikipedia-2-7-50538-release%2Fwikipedia-2-7-50538-samsung-2025-06-24-android-apk-download%2F" target="_blank" class="facebook-color" rel="nofollow noopener"><svg class="clickable icon" aria-label="Facebook">
                                    <use xlink:href="#apkm-icon-facebook"></use>
                                </svg></a>
                            <a href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fwww.apkmirror.com%2Fapk%2Fwikimedia-foundation%2Fwikipedia%2Fwikipedia-2-7-50538-release%2Fwikipedia-2-7-50538-samsung-2025-06-24-android-apk-download%2F&amp;text=Just+downloaded+the+Wikipedia****.50538-samsung-2025-06-24+%28nodpi%29+%28Android****%2B%29+APK+from+%40APKMirror" target="_blank" class="twitter-color" rel="nofollow noopener" aria-label="Twitter"><svg class="clickable icon">
                                    <use xlink:href="#apkm-icon-twitter"></use>
                                </svg></a>
                                                        <a href="https://www.reddit.com/submit?url=https%3A%2F%2Fwww.apkmirror.com%2Fapk%2Fwikimedia-foundation%2Fwikipedia%2Fwikipedia-2-7-50538-release%2Fwikipedia-2-7-50538-samsung-2025-06-24-android-apk-download%2F&amp;title=Just+downloaded+the+Wikipedia****.50538-samsung-2025-06-24+%28nodpi%29+%28Android****%2B%29+APK+from+%40APKMirror" target="_blank" class="reddit-color" rel="nofollow noopener" aria-label="Reddit"><svg class="clickable icon">
                                    <use xlink:href="#apkm-icon-reddit"></use>
                                </svg></a>
                                                        <a href="https://www.linkedin.com/shareArticle?mini=true&amp;url=https%3A%2F%2Fwww.apkmirror.com%2Fapk%2Fwikimedia-foundation%2Fwikipedia%2Fwikipedia-2-7-50538-release%2Fwikipedia-2-7-50538-samsung-2025-06-24-android-apk-download%2F&amp;title=Just+downloaded+the+Wikipedia****.50538-samsung-2025-06-24+%28nodpi%29+%28Android****%2B%29+APK+from+%40APKMirror" target="_blank" class="linkedin-color" rel="nofollow noopener" aria-label="Linkedin"><svg class="clickable icon">
                                    <use xlink:href="#apkm-icon-linkedin"></use>
                                </svg></a>
                            <a href="https://telegram.me/share/url?url=https%3A%2F%2Fwww.apkmirror.com%2Fapk%2Fwikimedia-foundation%2Fwikipedia%2Fwikipedia-2-7-50538-release%2Fwikipedia-2-7-50538-samsung-2025-06-24-android-apk-download%2F&amp;text=Just+downloaded+the+Wikipedia****.50538-samsung-2025-06-24+%28nodpi%29+%28Android****%2B%29+APK+from+%40APKMirror" target="_blank" class="telegram-color" rel="nofollow noopener" aria-label="Telegram"><svg class="clickable icon">
                                    <use xlink:href="#apkm-icon-telegram"></use>
                                </svg></a>
                            <a href="whatsapp://send?text=Just+downloaded+the+Wikipedia****.50538-samsung-2025-06-24+%28nodpi%29+%28Android****%2B%29+APK+from+%40APKMirror%0Ahttps%3A%2F%2Fwww.apkmirror.com%2Fapk%2Fwikimedia-foundation%2Fwikipedia%2Fwikipedia-2-7-50538-release%2Fwikipedia-2-7-50538-samsung-2025-06-24-android-apk-download%2F" target="_blank" class="whatsapp-color" rel="nofollow noopener" aria-label="Whatsapp"><svg class="clickable icon">
                                    <use xlink:href="#apkm-icon-whatsapp"></use>
                                </svg></a>
                            <a href="mailto:?subject=Just+downloaded+the+Wikipedia****.50538-samsung-2025-06-24+%28nodpi%29+%28Android****%2B%29+APK+from+%40APKMirror&body=Just+downloaded+the+Wikipedia****.50538-samsung-2025-06-24+%28nodpi%29+%28Android****%2B%29+APK+from+%40APKMirror%0Ahttps%3A%2F%2Fwww.apkmirror.com%2Fapk%2Fwikimedia-foundation%2Fwikipedia%2Fwikipedia-2-7-50538-release%2Fwikipedia-2-7-50538-samsung-2025-06-24-android-apk-download%2F" target="_blank" class="email-color" rel="nofollow noopener" aria-label="Email">
                                <span>@</span>
                            </a>
                        </div>
                    </li>
                </ul>
            </div>
            <a target="_blank" title="View on Play Store" alt="View on Play Store" href="https://play.google.com/store/apps/details?id=org.wikipedia" class="accent_color tab-button" rel="nofollow">
                <svg class="icon playstore-icon">
                    <use xlink:href="#apkm-icon-playstore"></use>
                </svg>
            </a>
                                    <div class="dropdown">
                <a role="button" title="View RSS feeds" alt="View RSS feeds" class="accent_color tab-button dropdown-toggle" id="rssDropdown" data-level=4 data-termid=315 data-postid="9899167" data-type="rss" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <svg class="icon icon-rss">
                        <use xlink:href="#apkm-icon-rss"></use>
                    </svg>
                </a>
                <ul class="dropdown-menu dropdown-menu-right addsmallpadding" id="rssDropdownPanel" aria-labelledby="rssDropdown">
                    <li>
                        <div class="showbox">
                            <div class="loader"><svg class="circular" viewBox="25 25 50 50">
                                    <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10" />
                                </svg></div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="dropdown">
                <a role="button" title="Subscribe via Pushbullet" alt="Subscribe via Pushbullet" class="accent_color tab-button dropdown-toggle" id="pbDropdown" data-level=4 data-termid=315 data-postid="9899167" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <svg class="icon share-icon">
                        <use xlink:href="#apkm-icon-pushbullet"></use>
                    </svg>
                </a>
                <ul class="dropdown-menu dropdown-menu-right addsmallpadding" id="pbDropdownPanel" aria-labelledby="pbDropdown">
                    <li>
                        <div class="showbox">
                            <div class="loader"><svg class="circular" viewBox="25 25 50 50">
                                    <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10" />
                                </svg></div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <div class="tab-content unroll">    <div role="tabpanel" class="tab-pane " >
        <a class="doc-anchor" name="file" data-spy="scroll" data-target=".scroll-spy-container"></a>
                    <div class="row d-flex f-a-start">
                <h2 class="f-100 tabs-header" title="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+) download free apk">Download Wikipedia 2.7.50538 APK</h2>
                                    <div class="f-sm-50">
                                                <div class="apk-detail-table wrapText">
                            <div class="appspec-row d-flex f-nowrap">
                                <span>
                                    <svg class="accent_color icon apkm-icon-file d-block" title="APK details" alt="APK details">
                                        <use xlink:href="#apkm-icon-file"></use>
                                    </svg>
                                </span>
                                <div class="appspec-value f-grow">
                                                                        Version: 2.7.50538-samsung-2025-06-24                                    (50538)
                                    <br><span class="wrapText">Languages:  <a href="#languages" data-toggle="modal" class="accent_color">160</a></span><br>
                                    <span class="wrapText">Package: org.wikipedia</span>
                                    <br>Downloads: 9                                </div>
                            </div>
                            <div class="appspec-row d-flex f-nowrap">
                                <span>
                                    <svg class="accent_color icon apkm-icon-filesize d-block" title="APK file size" alt="APK file size">
                                        <use xlink:href="#apkm-icon-filesize"></use>
                                    </svg>
                                </span>
                                <div class="appspec-value f-grow">
                                    42.69 MB (44,760,913 bytes)<br>Supports installation on external storage.                                </div>
                            </div>
                                                            <div class="appspec-row d-flex f-nowrap">
                                    <span>
                                        <svg class="accent_color icon apkm-icon-sdk d-block" title="Android version" alt="Android version">
                                            <use xlink:href="#apkm-icon-sdk"></use>
                                        </svg>
                                    </span>
                                    <div class="appspec-value f-grow">
                                        <div class="">
                                            Min: Android 5.0 (Lollipop, API 21)<br>Target: Android 15 (API 35)                                        </div>
                                    </div>
                                </div>
                                                        <div class="appspec-row d-flex f-nowrap">
                                <span>
                                    <svg class="accent_color icon apkm-icon-dpi d-block" title="Supported architectures and screen densities" alt="Supported architectures and screen densities">
                                        <use xlink:href="#apkm-icon-dpi"></use>
                                    </svg>
                                </span>
                                <div class="appspec-value f-grow">
                                    arm64-v8a + armeabi-v7a + x86 + x86_64<br>nodpi                                </div>
                            </div>
                            <div class="appspec-row d-flex f-nowrap">
                                <span>
                                    <svg class="accent_color icon apkm-icon-safe d-block" title="MD5, SHA-1, SHA-256 signatures" alt="MD5, SHA-1, SHA-256 signatures">
                                        <use xlink:href="#apkm-icon-safe"></use>
                                    </svg>
                                </span>
                                <div class="appspec-value f-grow wordbreak-all"><a class="accent_color" href="#safeDownload" data-toggle="modal">MD5, SHA-1, SHA-256
                                        signatures</a></div>
                            </div>
                                                            <div class="appspec-row d-flex f-nowrap">
                                    <span>
                                        <svg class="accent_color icon apkm-icon-lock d-block" title="APK permissions, features and libraries" alt="APK permissions, features and libraries">
                                            <use xlink:href="#apkm-icon-lock"></use>
                                        </svg>
                                    </span>
                                    <div class="appspec-value f-grow wordbreak-all ucfirst appspec-permissions">
                                        <span>Permissions: <a class="accent_color" href="#apkPermissions" data-toggle="modal">18</a></span><span>Features: <a class="accent_color" href="#apkPermissions" data-toggle="modal">4</a></span><span>Libraries: <a class="accent_color" href="#apkPermissions" data-toggle="modal">4</a></span>                                    </div>
                                </div>
                                                            <div class="appspec-row d-flex f-nowrap">
                                <span>
                                    <svg class="accent_color icon apkm-icon-calendar d-block" title="Upload details" alt="Upload details">
                                        <use xlink:href="#apkm-icon-calendar"></use>
                                    </svg>
                                </span>
                                <div class="appspec-value f-grow">
                                    Uploaded
                                    <span style="" class="datetime_utc" data-utcdate="07/8/2025 03:47 UTC">July 8, 2025 at 3:47AM UTC</span> by FavMaster98                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="center f-sm-50">
                        <div style="overflow:hidden;">
                                                            <div style="margin-top: 10px;">
                                        <p><span class="pushbullet-subscribe-widget abtn disabled hidden alignleft" data-description="Subscribe to all Wikipedia Updates provided by APK Mirror" data-channel-name="Wikipedia App Updates" data-image-url="https://downloadr2.apkmirror.com/wp-content/uploads/2019/10/665878e0603d5_org.wikipedia.png" data-tag="am242610787" data-feed-url="http://www.apkmirror.com/apk/wikimedia-foundation/wikipedia/feed/" data-widget="button" data-size="large" href="#">
            <svg class="icon inline-icon icon-spin">
                <use xlink:href="#apkm-icon-pushbullet"></use>
            </svg><span>Wikipedia App Updates</span></span></p>
                                    </div>
                                <div style="margin-top: 10px; margin-bottom: 20px;">
                                        <p><span class="pushbullet-subscribe-widget abtn disabled hidden alignleft" data-description="Subscribe to all Wikimedia Foundation Updates provided by APK Mirror" data-channel-name="Wikimedia Foundation Dev Updates" data-image-url="https://www.apkmirror.com/wp-content/themes/APKMirror/images/pblogo.png" data-tag="am-36107813" data-feed-url="http://www.apkmirror.com/apk/wikimedia-foundation/feed/" data-widget="button" data-size="large" href="#">
            <svg class="icon inline-icon icon-spin">
                <use xlink:href="#apkm-icon-pushbullet"></use>
            </svg><span>Wikimedia Foundation Dev Updates</span></span></p>
                                    </div>
                                                        <div class='ains ains-15 GEo  ains-has-label ains-apkm_apk_page_downloadtab ai-track' data-ai='WzE1LDEsImFwa21fYXBrX3BhZ2VfZG93bmxvYWR0YWIiLCJhcGttX2dvb2dsZV9hZHMiLDFd'>
<span class="advertisement-text">Advertisement</span><a href="/premium" class="advertisement-text membership-text" title="APKMirror Premium" data-google-vignette="false"><span>Remove ads, dark theme, and more with <img src="/wp-content/themes/APKMirror/images/apple-touch-icon-57x57.png" alt="APKM Premium" width="18" height="18" /> Premium</span></a>
<div class="gooWidget yen google-ad-square">
  <!-- Adsense responsive ad apkm_apk_page_downloadtab -->
  <ins class="adsbygoogle adslot NJx"
    style="display:block"
    data-ad-client="ca-pub-8776668743582988"
    data-ad-slot="6398035288"
    data-ad-region="REGION_1"></ins>
  <script>
    APKM.a(116, '15');
  </script>
</div></div>
                            <div class="hidden pHr">
                                                            </div>

                                                            <div style="display: block; text-align: center; margin-top: 20px; clear: both">
                                    <svg class="accent_color icon inline-icon apkm-icon-safe">
                                        <use xlink:href="#apkm-icon-safe"></use>
                                    </svg>
                                    <a class="accent_color downloadSafe" href="#safeDownload" data-toggle="modal"><span class="downloadSafeText">Verified safe to install (read more)</span></a>
                                </div>
                                                                    <a rel="nofollow" class="accent_bg btn btn-flat downloadButton pNa" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/download/?key=894f0048f32a14c9b93bc0727b898513d6d99f3b">
                                        <svg class="icon download-button-icon">
                                            <use xlink:href="#apkm-icon-download"></use>
                                        </svg>
                                        Download APK <br><span class="downloadButtonSubtitle">42.69 MB</span>                                    </a>
                                                                <div style="display: block; text-align: center;">
                                                            </div>
                        </div>
                    </div>
                            </div>
        
        <div class="t-height KJv pHr expandMargins hidden">
            <p></p>
            <div class="progress progress-striped active ">
                <div id="downloadCountdownBar" class="progress-bar"></div>
            </div>
            <div class="table-row">
                <div style="width: 150px;" class="table-cell">
                    <img src="/wp-content/themes/APKMirror/images/sadkitten.png" alt="" width="120" height="120" loading="lazy">
                </div>
                <div class="table-cell dowrap">
                    <strong>
                        <h3>Whoa there!</h3>
                        <p>It looks like you're using an ad blocker, so you'll have to wait <span class="fontBlue IHl">15 more sec</span>.<br>
                            <a href="/premium" class="fontBlue">Hide ads with <img src="/wp-content/themes/APKMirror/images/apple-touch-icon-57x57.png" alt="APKM Premium" width="18" height="18" /> Premium</a> to skip the wait and help us pay for bandwidth, hosting, and other bills.<br>
                            Or please add this site to your allowlist (<a href="https://adblockplus.org/en/getting_started#disabling" target="_blank" class="fontBlue">AdBlock Plus</a>,
                            <a href="https://www.apkmirror.com/wp-content/themes/APKMirror/images/adblock_disable_ublock.png" target="_blank" class="fontBlue">uBlock Origin</a>)
                        </p>
                    </strong>
                </div>
            </div>
        </div>
    </div>
    <div role="tabpanel" class="tab-pane " >
        <a class="doc-anchor" name="notes" data-spy="scroll" data-target=".scroll-spy-container"></a>
        <div class="row">
                        <h3 class="tabs-header" title="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+) notes">Wikipedia notes</h3>
                        <div class="notes wrapText ">
                                <div class="widgetHeader noMargins noPadding">APK Notes:</div><p><p>General bug fixes and enhancements.</p>
</p>
                            </div>
        </div>
    </div>
    <div role="tabpanel" class="tab-pane " >
        <a class="doc-anchor" name="whatsnew" data-spy="scroll" data-target=".scroll-spy-container"></a>
        <div class="row">
                        <h3 class="tabs-header" title="What's new in Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)">What's new in Wikipedia 2.7.50538</h3>
                        <div class="notes wrapText ">
                                <p>- General bug fixes and enhancements.</p>
                            </div>
        </div>
    </div>
    <div role="tabpanel" class="tab-pane " >
        <a class="doc-anchor" name="description" data-spy="scroll" data-target=".scroll-spy-container"></a>
        <div class="row">
                        <h3 class="tabs-header" title="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+) description">About Wikipedia 2.7.50538</h3>
                        <div class="notes wrapText collapsable collapsed">
                                <p>The best Wikipedia experience on your Mobile device. Ad-free and free of charge, forever. With the official Wikipedia app, you can search and explore 40+ million articles in 300+ languages, no matter where you are.</p><div class='ains ains-18 GEo  ains-has-label ains-apkm_apk_page_descriptiontab ai-viewport-1 ai-viewport-5 ai-track' data-ai='WzE4LDEsImFwa21fYXBrX3BhZ2VfZGVzY3JpcHRpb250YWIiLCJhcGttX2dvb2dsZV9hZHMiLDFd'>
<span class="advertisement-text">Advertisement</span><a href="/premium" class="advertisement-text membership-text" title="APKMirror Premium" data-google-vignette="false"><span>Remove ads, dark theme, and more with <img src="/wp-content/themes/APKMirror/images/apple-touch-icon-57x57.png" alt="APKM Premium" width="18" height="18" /> Premium</span></a>
<div class="gooWidget yen google-ad-square-inline x-showOnTabletAndDesktop">
  <!-- Adsense responsive ad apkm_apk_page_descriptiontab -->
  <ins class="adsbygoogle adslot NJx"
    style="display:block"
    data-ad-client="ca-pub-8776668743582988"
    data-ad-slot="**********"
    data-ad-region="REGION_2"></ins>
  <script>
    APKM.a(119, '18');
  </script>
</div></div>

<p>== Why you'll love the this app ==</p>
<p>1. It's free and open<br />
Wikipedia is the encyclopedia that anyone can edit. Articles on Wikipedia are freely licensed and the app code is 100% open source. The heart and soul of Wikipedia is a community of people working to bring you unlimited access to free, reliable and neutral information.</p>
<p>2. No ads<br />
Wikipedia is a place to learn, not a place for advertising. This app is made by the Wikimedia Foundation, a nonprofit organization that supports and operates Wikipedia. We provide this service in the pursuit of open knowledge that’s always ad-free and never tracks your data. </p>
<p>3. Read in your language<br />
Search 40 million articles in over 300 languages in the world’s largest source of information. Set your preferred languages in the app and easily switch between them when browsing or reading. </p>
<p>4. Use it offline<br />
Save your favorite articles and read Wikipedia offline with “My lists“. Name lists as you like and collect articles across different languages. Saved articles and reading lists are synced across all your devices and are available even when you do not have internet connectivity.</p>
<p>5. Attention to detail and night mode<br />
The app embraces Wikipedia’s simplicity and adds delight to it. A beautiful and distraction-free interface lets you focus on the essential: reading articles. With text size adjustment and themes in pure black, dark, sepia or light, you can choose the most pleasant reading experience for you.</p>
<p>== Broaden your horizon with these features == </p>
<p>1. Customize your Explore feed<br />
"Explore" lets you see recommended Wikipedia content including current events, popular articles, captivating freely-licensed photos, events on this day in history, suggested articles based on your reading history, and more.</p>
<p>2. Find and search<br />
Quickly find what you're looking for by searching within articles or with the search bar at the top of the app. You can even search using your favorite emojis or voice-enabled search.</p>
<p>== We'd love your feedback ==</p>
<p>1. To send feedback from the app:<br />
In the menu, press "Settings", then, in the "About" section, tap "Send app feedback".</p>
<p>2. If you have experience with Java and the Android SDK, then we look forward to your contributions! More info: <a href="https://mediawiki.org/wiki/Wikimedia_Apps/Team/Android/App_hacking" rel="nofollow noopener" target="_blank">mediawiki.org/wiki/Wikimedia_Apps/Team/Android/App_hacking</a> </p>
<p>3. Explanation of permissions needed by the app: <a href="https://mediawiki.org/wiki/Wikimedia_Apps/Android_FAQ#Security_and_Permissions" rel="nofollow noopener" target="_blank">mediawiki.org/wiki/Wikimedia_Apps/Android_FAQ#Security_and_Permissions</a> </p>
<p>4. Privacy policy: <a href="https://m.wikimediafoundation.org/wiki/Privacy_policy" rel="nofollow noopener" target="_blank">m.wikimediafoundation.org/wiki/Privacy_policy</a> </p>
<p>5. Terms of Use: <a href="https://m.wikimediafoundation.org/wiki/Terms_of_Use" rel="nofollow noopener" target="_blank">m.wikimediafoundation.org/wiki/Terms_of_Use</a> </p>
<p>6. About the Wikimedia Foundation:<br />
The Wikimedia Foundation is a charitable nonprofit organization that supports and operates Wikipedia and the other Wiki projects. It is funded mainly through donations. For more information, please visit our website: <a href="https://wikimediafoundation.org/" rel="nofollow noopener" target="_blank">wikimediafoundation.org/</a></p>
                    <div class="show-more d-flex f-100 accent_color">
        <a href="#" data-toggle="show-more" class="accent_color f-a-end more">Show more
            <svg class="icon chevron-icon down">
                <use xlink:href="#apkm-icon-chevron"></use>
            </svg>
        </a>
        <a href="#" data-toggle="show-more" class="accent_color f-a-end less">Show less
            <svg class="icon chevron-icon up">
                <use xlink:href="#apkm-icon-chevron"></use>
            </svg>
        </a>
    </div>
            </div>
        </div>
    </div>
        <div role="tabpanel" class="tab-pane " >
            <div class="row">
                <a class="doc-anchor" name="gallery" data-spy="scroll" data-target=".scroll-spy-container"></a>
                <h3 class="tabs-header" title="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+) screenshots">Wikipedia 2.7.50538 screenshots (9)</h3>
                                <div class="d-flex f-nowrap f-start gallery-container">
                    <span class="img-container"><img width="169" height="300" src="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-169x300.png" class="attachment-medium size-medium" alt="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)" data-description="Wikipedia" data-lightbox="screenshots" loading="lazy" decoding="async" srcset="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-169x300.png 169w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-576x1024.png 576w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia.png 810w" sizes="auto, (max-width: 169px) 100vw, 169px" /></span><span class="img-container"><img width="169" height="300" src="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-2-169x300.png" class="attachment-medium size-medium" alt="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)" data-description="Wikipedia" data-lightbox="screenshots" loading="lazy" decoding="async" srcset="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-2-169x300.png 169w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-2-576x1024.png 576w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-2.png 810w" sizes="auto, (max-width: 169px) 100vw, 169px" /></span><span class="img-container"><img width="169" height="300" src="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-3-169x300.png" class="attachment-medium size-medium" alt="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)" data-description="Wikipedia" data-lightbox="screenshots" loading="lazy" decoding="async" srcset="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-3-169x300.png 169w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-3-576x1024.png 576w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-3.png 810w" sizes="auto, (max-width: 169px) 100vw, 169px" /></span><span class="img-container"><img width="169" height="300" src="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-4-169x300.png" class="attachment-medium size-medium" alt="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)" data-description="Wikipedia" data-lightbox="screenshots" loading="lazy" decoding="async" srcset="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-4-169x300.png 169w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-4-576x1024.png 576w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-4.png 810w" sizes="auto, (max-width: 169px) 100vw, 169px" /></span><span class="img-container"><img width="169" height="300" src="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-5-169x300.png" class="attachment-medium size-medium" alt="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)" data-description="Wikipedia" data-lightbox="screenshots" loading="lazy" decoding="async" srcset="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-5-169x300.png 169w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-5-576x1024.png 576w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-5.png 810w" sizes="auto, (max-width: 169px) 100vw, 169px" /></span><span class="img-container"><img width="169" height="300" src="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-6-169x300.png" class="attachment-medium size-medium" alt="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)" data-description="Wikipedia" data-lightbox="screenshots" loading="lazy" decoding="async" srcset="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-6-169x300.png 169w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-6-576x1024.png 576w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-6.png 810w" sizes="auto, (max-width: 169px) 100vw, 169px" /></span><span class="img-container"><img width="169" height="300" src="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-7-169x300.png" class="attachment-medium size-medium" alt="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)" data-description="Wikipedia" data-lightbox="screenshots" loading="lazy" decoding="async" srcset="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-7-169x300.png 169w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-7-576x1024.png 576w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-7.png 810w" sizes="auto, (max-width: 169px) 100vw, 169px" /></span><span class="img-container"><img width="169" height="300" src="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-8-169x300.png" class="attachment-medium size-medium" alt="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)" data-description="Wikipedia" data-lightbox="screenshots" loading="lazy" decoding="async" srcset="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-8-169x300.png 169w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-8-576x1024.png 576w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-8.png 810w" sizes="auto, (max-width: 169px) 100vw, 169px" /></span><span class="img-container"><img width="169" height="300" src="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-9-169x300.png" class="attachment-medium size-medium" alt="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)" data-description="Wikipedia" data-lightbox="screenshots" loading="lazy" decoding="async" srcset="https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-9-169x300.png 169w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-9-576x1024.png 576w, https://downloadr2.apkmirror.com/wp-content/uploads/2025/03/16/Wikipedia-org.wikipedia-9.png 810w" sizes="auto, (max-width: 169px) 100vw, 169px" /></span></div></div></div><div role="tabpanel" class="tab-pane"><div class="row"><h3 class="tabs-header" title="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+) screenshots">Wikipedia 2.7.50538 trailer</h3><div class="d-flex f-nowrap gallery-container"><div class="responsive-embed">
                        <iframe src="https://www.youtube.com/embed/6UkFHYqVsbc" allow="fullscreen" alt="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+) trailer"></iframe>
                      </div>                </div>
            </div>
        </div>
            <div role="tabpanel" class="tab-pane noPadding " >
            <a class="doc-anchor" name="variants" data-spy="scroll" data-target=".scroll-spy-container"></a>
            <h3 class="addpadding tabs-header" title="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+) variants">Wikipedia 2.7.50538 variants</h3>
            <p class="indented-text">This release comes in several variants (we currently have 2). Consult our <a class="accent_color" href="/faq/">handy FAQ</a> to see which download is right for you.</p>        <div style="overflow-x: auto;">
            <div class="table topmargin variants-table">
                <div class="table-row headerFont">
                    <div class="table-cell rowheight addseparator expand variant-min-width pad">Variant</div>
                    <div class="table-cell rowheight addseparator expand pad"><span class="visible-xs-inline">Arch</span><span class="hidden-xs">Architecture</span></div>
                    <div class="table-cell rowheight addseparator expand pad"><span class="visible-xs-inline">Version</span><span class="hidden-xs">Minimum Version</span></div>
                    <div class="table-cell rowheight addseparator expand pad"><span class="visible-xs-inline">DPI</span><span class="hidden-xs">Screen DPI</span></div>
                    <div class="table-cell rowheight addseparator expand pad"></div>
                </div>
                                <div class="table-row headerFont">
                        <div class="table-cell rowheight addseparator expand pad dowrap-break-all">
                            <a class="accent_color" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-r-2025-06-24-android-apk-download/">
                                <svg class="icon tag-icon">
                                    <use xlink:href="#apkm-icon-tag"></use>
                                </svg>
                                2.7.50538-r-2025-06-24                            </a>
                            <span class="apkm-badge success" data-apkm-tooltip="APK bundle with base APK and 11 splits">BUNDLE</span>  <span class="apkm-badge success" data-apkm-tooltip="11 splits">11 S</span>    <span class="bubble p-static inlineBlock"><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-r-2025-06-24-android-apk-download/#disqus_thread" data-disqus-identifier="9804089 https://www.apkmirror.com/?post_type=apps_post&#038;p=9804089" title="View comments"></a></span>                            <br><span
                                    class="colorLightBlack">50538</span>
                            <br><span
                                    class="colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="07/5/2025 17:38 UTC">July 5, 2025</span></span>
                        </div>
                        <div class="table-cell rowheight addseparator expand pad dowrap">universal</div>
                        <div class="table-cell rowheight addseparator expand pad dowrap">Android 5.0+</div>
                        <div class="table-cell rowheight addseparator expand pad dowrap">120-640dpi</div>
                        <div class="table-cell rowheight addseparator expand pad dowrap">
                            <a class="accent_color" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-r-2025-06-24-android-apk-download/" data-google-interstitial="false">
                                <svg class="icon download-button-icon">
                                    <use xlink:href="#apkm-icon-download"></use>
                                </svg>
                            </a>
                        </div>
                    </div>
                                        <div class="table-row headerFont">
                        <div class="table-cell rowheight addseparator expand pad dowrap-break-all">
                            <a class="accent_color" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/">
                                <svg class="icon tag-icon">
                                    <use xlink:href="#apkm-icon-tag"></use>
                                </svg>
                                2.7.50538-samsung-2025-06-24                            </a>
                            <span class="apkm-badge">APK</span>   <span class="bubble p-static inlineBlock"><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/#disqus_thread" data-disqus-identifier="9899167 https://www.apkmirror.com/?post_type=apps_post&#038;p=9899167" title="View comments"></a></span>                            <br><span
                                    class="colorLightBlack">50538</span>
                            <br><span
                                    class="colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="07/8/2025 03:47 UTC">July 8, 2025</span></span>
                        </div>
                        <div class="table-cell rowheight addseparator expand pad dowrap">universal</div>
                        <div class="table-cell rowheight addseparator expand pad dowrap">Android 5.0+</div>
                        <div class="table-cell rowheight addseparator expand pad dowrap">nodpi</div>
                        <div class="table-cell rowheight addseparator expand pad dowrap">
                            <a class="accent_color" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/" data-google-interstitial="false">
                                <svg class="icon download-button-icon">
                                    <use xlink:href="#apkm-icon-download"></use>
                                </svg>
                            </a>
                        </div>
                    </div>
                                </div>
        </div>
                </div>
    </div></div>
                <div class="listWidget">
            <div style="position: relative; display: inline-block;">
                <div class="widgetHeader">
                    Apps related to Wikipedia                </div>
            </div>
                    <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia (f-droid version) r/2.7.50536-r-2025-06-18"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2018%2F04%2F5acb8bdc98921.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2018%2F04%2F5acb8bdc98921.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2018%2F04%2F5acb8bdc98921.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2018%2F04%2F5acb8bdc98921.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia-fdroid-version/#disqus_thread" data-disqus-identifier="9747097 http://www.apkmirror.com/?post_type=app_release&#038;p=9747097" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Wikipedia (f-droid version)" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia-fdroid-version/">Wikipedia (f-droid version)</a>                                         </h5>
                                                        </div>
                                <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia-fdroid-version/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">r/2.7.50536-r-2025-06-18                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="06/22/2025 22:47 UTC">June 22, 2025 at 10:47PM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">33.84 MB</span>
                </p>
                            </div>
                </div>
            </div>
        
        
        <div style="margin-top: 15px;"></div>
                            <div class="listWidget p-relative">
                        <a class="doc-anchor" name="previous_apks" data-spy="scroll" data-target=".scroll-spy-container"></a>                                                <div class="widgetHeader d-flex f-start f-a-start">Previous APKs for (universal) (nodpi) (Android 5.0+) variant                                                    </div>
                                <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/#disqus_thread" data-disqus-identifier="9899167 https://www.apkmirror.com/?post_type=apps_post&#038;p=9899167" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/">Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)</a> <span class="apkm-badge">APK</span>                                           </h5>
                                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="07/8/2025 03:47 UTC">July 8, 2025</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="07/8/2025 03:47 UTC">July 8, 2025</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50538(50538) for Android 5.0+ (Lollipop, API 21)                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="07/8/2025 03:47 UTC">July 8, 2025 at 3:47AM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">42.69 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">9                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50536-huawei-2025-06-18 (nodpi) (Android 5.0+)"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50536-release/wikipedia-2-7-50536-huawei-2025-06-18-android-apk-download/#disqus_thread" data-disqus-identifier="9742581 https://www.apkmirror.com/?post_type=apps_post&#038;p=9742581" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Wikipedia 2.7.50536-huawei-2025-06-18 (nodpi) (Android 5.0+)" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50536-release/wikipedia-2-7-50536-huawei-2025-06-18-android-apk-download/">Wikipedia 2.7.50536-huawei-2025-06-18 (nodpi) (Android 5.0+)</a> <span class="apkm-badge">APK</span>                                           </h5>
                                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="06/21/2025 21:13 UTC">June 21, 2025</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="06/21/2025 21:13 UTC">June 21, 2025</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50536-release/wikipedia-2-7-50536-huawei-2025-06-18-android-apk-download/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50536(50536) for Android 5.0+ (Lollipop, API 21)                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="06/21/2025 21:13 UTC">June 21, 2025 at 9:13PM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">36.62 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">25                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50529-samsung-2025-04-21 (nodpi) (Android 5.0+)"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50529-release/wikipedia-2-7-50529-samsung-2025-04-21-android-apk-download/#disqus_thread" data-disqus-identifier="9170150 https://www.apkmirror.com/?post_type=apps_post&#038;p=9170150" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Wikipedia 2.7.50529-samsung-2025-04-21 (nodpi) (Android 5.0+)" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50529-release/wikipedia-2-7-50529-samsung-2025-04-21-android-apk-download/">Wikipedia 2.7.50529-samsung-2025-04-21 (nodpi) (Android 5.0+)</a> <span class="apkm-badge">APK</span>                                           </h5>
                                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="04/30/2025 06:01 UTC">April 30, 2025</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="04/30/2025 06:01 UTC">April 30, 2025</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50529-release/wikipedia-2-7-50529-samsung-2025-04-21-android-apk-download/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50529(50529) for Android 5.0+ (Lollipop, API 21)                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="04/30/2025 06:01 UTC">April 30, 2025 at 6:01AM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">35.49 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">41                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50524-huawei-2025-03-04 (nodpi) (Android 5.0+)"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50524-release/wikipedia-2-7-50524-huawei-2025-03-04-android-apk-download/#disqus_thread" data-disqus-identifier="8905312 https://www.apkmirror.com/?post_type=apps_post&#038;p=8905312" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Wikipedia 2.7.50524-huawei-2025-03-04 (nodpi) (Android 5.0+)" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50524-release/wikipedia-2-7-50524-huawei-2025-03-04-android-apk-download/">Wikipedia 2.7.50524-huawei-2025-03-04 (nodpi) (Android 5.0+)</a> <span class="apkm-badge">APK</span>                                           </h5>
                                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="04/4/2025 06:47 UTC">April 4, 2025</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="04/4/2025 06:47 UTC">April 4, 2025</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50524-release/wikipedia-2-7-50524-huawei-2025-03-04-android-apk-download/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50524(50524) for Android 5.0+ (Lollipop, API 21)                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="04/4/2025 06:47 UTC">April 4, 2025 at 6:47AM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">34.35 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">57                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50495-samsung-2024-07-23 (nodpi) (Android 5.0+)"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50495-release/wikipedia-2-7-50495-samsung-2024-07-23-android-apk-download/#disqus_thread" data-disqus-identifier="6897948 https://www.apkmirror.com/?post_type=apps_post&#038;p=6897948" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Wikipedia 2.7.50495-samsung-2024-07-23 (nodpi) (Android 5.0+)" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50495-release/wikipedia-2-7-50495-samsung-2024-07-23-android-apk-download/">Wikipedia 2.7.50495-samsung-2024-07-23 (nodpi) (Android 5.0+)</a> <span class="apkm-badge">APK</span>                                           </h5>
                                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="08/20/2024 05:49 UTC">August 20, 2024</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="08/20/2024 05:49 UTC">August 20, 2024</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50495-release/wikipedia-2-7-50495-samsung-2024-07-23-android-apk-download/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50495(50495) for Android 5.0+ (Lollipop, API 21)                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="08/20/2024 05:49 UTC">August 20, 2024 at 5:49AM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">32.28 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">102                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50492-r-2024-06-11 (nodpi) (Android 5.0+)"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50492-release/wikipedia-2-7-50492-r-2024-06-11-2-android-apk-download/#disqus_thread" data-disqus-identifier="6526538 http://www.apkmirror.com/?post_type=apps_post&#038;p=6526538" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Wikipedia 2.7.50492-r-2024-06-11 (nodpi) (Android 5.0+)" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50492-release/wikipedia-2-7-50492-r-2024-06-11-2-android-apk-download/">Wikipedia 2.7.50492-r-2024-06-11 (nodpi) (Android 5.0+)</a> <span class="apkm-badge">APK</span>                                           </h5>
                                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="06/26/2024 17:15 UTC">June 26, 2024</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="06/26/2024 17:15 UTC">June 26, 2024</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50492-release/wikipedia-2-7-50492-r-2024-06-11-2-android-apk-download/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50492(50492) for Android 5.0+ (Lollipop, API 21)                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="06/26/2024 17:15 UTC">June 26, 2024 at 5:15PM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">31.37 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">195                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50490-r-2024-05-28 (nodpi) (Android 5.0+)"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50490-release/wikipedia-2-7-50490-r-2024-05-28-android-apk-download/#disqus_thread" data-disqus-identifier="6377048 http://www.apkmirror.com/?post_type=apps_post&#038;p=6377048" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Wikipedia 2.7.50490-r-2024-05-28 (nodpi) (Android 5.0+)" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50490-release/wikipedia-2-7-50490-r-2024-05-28-android-apk-download/">Wikipedia 2.7.50490-r-2024-05-28 (nodpi) (Android 5.0+)</a> <span class="apkm-badge">APK</span>                                           </h5>
                                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="06/5/2024 18:24 UTC">June 5, 2024</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="06/5/2024 18:24 UTC">June 5, 2024</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50490-release/wikipedia-2-7-50490-r-2024-05-28-android-apk-download/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50490(50490) for Android 5.0+ (Lollipop, API 21)                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="06/5/2024 18:24 UTC">June 5, 2024 at 6:24PM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">31.33 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">114                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50489-samsung-2024-05-20 (nodpi) (Android 5.0+)"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50489-release/wikipedia-2-7-50489-samsung-2024-05-20-android-apk-download/#disqus_thread" data-disqus-identifier="6309826 https://www.apkmirror.com/?post_type=apps_post&#038;p=6309826" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Wikipedia 2.7.50489-samsung-2024-05-20 (nodpi) (Android 5.0+)" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50489-release/wikipedia-2-7-50489-samsung-2024-05-20-android-apk-download/">Wikipedia 2.7.50489-samsung-2024-05-20 (nodpi) (Android 5.0+)</a> <span class="apkm-badge">APK</span>                                           </h5>
                                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="05/26/2024 03:09 UTC">May 26, 2024</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="05/26/2024 03:09 UTC">May 26, 2024</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50489-release/wikipedia-2-7-50489-samsung-2024-05-20-android-apk-download/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50489(50489) for Android 5.0+ (Lollipop, API 21)                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="05/26/2024 03:09 UTC">May 26, 2024 at 3:09AM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">31.15 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">14                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50489-r-2024-05-20 (nodpi) (Android 5.0+)"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50489-release/wikipedia-2-7-50489-r-2024-05-20-android-apk-download/#disqus_thread" data-disqus-identifier="6283271 http://www.apkmirror.com/?post_type=apps_post&#038;p=6283271" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Wikipedia 2.7.50489-r-2024-05-20 (nodpi) (Android 5.0+)" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50489-release/wikipedia-2-7-50489-r-2024-05-20-android-apk-download/">Wikipedia 2.7.50489-r-2024-05-20 (nodpi) (Android 5.0+)</a> <span class="apkm-badge">APK</span>                                           </h5>
                                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="05/22/2024 15:08 UTC">May 22, 2024</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="05/22/2024 15:08 UTC">May 22, 2024</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50489-release/wikipedia-2-7-50489-r-2024-05-20-android-apk-download/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50489(50489) for Android 5.0+ (Lollipop, API 21)                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="05/22/2024 15:08 UTC">May 22, 2024 at 3:08PM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">31.31 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">73                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50484-r-2024-04-19 (nodpi) (Android 5.0+)"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50484-release/wikipedia-2-7-50484-r-2024-04-19-2-android-apk-download/#disqus_thread" data-disqus-identifier="6132591 http://www.apkmirror.com/?post_type=apps_post&#038;p=6132591" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Wikipedia 2.7.50484-r-2024-04-19 (nodpi) (Android 5.0+)" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50484-release/wikipedia-2-7-50484-r-2024-04-19-2-android-apk-download/">Wikipedia 2.7.50484-r-2024-04-19 (nodpi) (Android 5.0+)</a> <span class="apkm-badge">APK</span>                                           </h5>
                                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="04/24/2024 20:52 UTC">April 24, 2024</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="04/24/2024 20:52 UTC">April 24, 2024</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50484-release/wikipedia-2-7-50484-r-2024-04-19-2-android-apk-download/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50484(50484) for Android 5.0+ (Lollipop, API 21)                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="04/24/2024 20:52 UTC">April 24, 2024 at 8:52PM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">31.03 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">100                            </span>
                        </p>
                                    </div>
                </div>
                                        <div style="padding-top: 7px; padding-bottom: 7px; height: 51px;" class="table noMargins">
                                        <div class="table-row">
                                            <div class="table-cell center">
                                                <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/variant-%7B%22arches_slug%22%3A%5B%22arm64-v8a%22%2C%22armeabi-v7a%22%2C%22x86%22%2C%22x86_64%22%5D%2C%22dpis_slug%22%3A%5B%22nodpi%22%5D%2C%22minapi_slug%22%3A%22minapi-21%22%7D/">See more uploads...</a>
                                            </div>
                                        </div>
                                    </div>
                                                        </div>
                                        <div class="listWidget p-relative">
                        <a class="doc-anchor" name="all_releases" data-spy="scroll" data-target=".scroll-spy-container"></a>                                                <div class="widgetHeader d-flex f-start f-a-start">All versions                                                    </div>
                                <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50538"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/#disqus_thread" data-disqus-identifier="9804090 https://www.apkmirror.com/?post_type=app_release&#038;p=9804090" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                            <div style="padding-top: 4px;">
                                            <h5 title="Wikipedia 2.7.50538" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/">Wikipedia 2.7.50538</a>                                         </h5>
                                        <div class="appRowVariantTag wrapText"><a class="accent_color" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/"><svg class="icon tag-icon"><use xlink:href="#apkm-icon-tag"></use></svg>2 variants</a></div>                        </div>
                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="07/8/2025 03:47 UTC">July 8, 2025</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="07/8/2025 03:47 UTC">July 8, 2025</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50538-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50538                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="07/8/2025 03:47 UTC">July 8, 2025 at 3:47AM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">38.89 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">39                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50536"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50536-release/#disqus_thread" data-disqus-identifier="9716009 https://www.apkmirror.com/?post_type=app_release&#038;p=9716009" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                            <div style="padding-top: 4px;">
                                            <h5 title="Wikipedia 2.7.50536" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50536-release/">Wikipedia 2.7.50536</a>                                         </h5>
                                        <div class="appRowVariantTag wrapText"><a class="accent_color" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50536-release/"><svg class="icon tag-icon"><use xlink:href="#apkm-icon-tag"></use></svg>2 variants</a></div>                        </div>
                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="06/21/2025 21:13 UTC">June 21, 2025</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="06/21/2025 21:13 UTC">June 21, 2025</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50536-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50536                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="06/21/2025 21:13 UTC">June 21, 2025 at 9:13PM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">32.45 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">53                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50535"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50535-release/#disqus_thread" data-disqus-identifier="9698889 https://www.apkmirror.com/?post_type=app_release&#038;p=9698889" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                            <div style="padding-top: 4px;">
                                            <h5 title="Wikipedia 2.7.50535" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50535-release/">Wikipedia 2.7.50535</a>                                         </h5>
                                                                </div>
                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="06/18/2025 12:06 UTC">June 18, 2025</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="06/18/2025 12:06 UTC">June 18, 2025</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50535-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50535                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="06/18/2025 12:06 UTC">June 18, 2025 at 12:06PM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">32.45 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">31                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50532"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50532-release/#disqus_thread" data-disqus-identifier="9396723 http://www.apkmirror.com/?post_type=app_release&#038;p=9396723" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                            <div style="padding-top: 4px;">
                                            <h5 title="Wikipedia 2.7.50532" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50532-release/">Wikipedia 2.7.50532</a>                                         </h5>
                                                                </div>
                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="05/20/2025 17:43 UTC">May 20, 2025</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="05/20/2025 17:43 UTC">May 20, 2025</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50532-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50532                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="05/20/2025 17:43 UTC">May 20, 2025 at 5:43PM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">31.52 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">84                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50530"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50530-release/#disqus_thread" data-disqus-identifier="9320379 https://www.apkmirror.com/?post_type=app_release&#038;p=9320379" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                            <div style="padding-top: 4px;">
                                            <h5 title="Wikipedia 2.7.50530" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50530-release/">Wikipedia 2.7.50530</a>                                         </h5>
                                                                </div>
                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="05/14/2025 03:18 UTC">May 14, 2025</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="05/14/2025 03:18 UTC">May 14, 2025</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50530-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50530                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="05/14/2025 03:18 UTC">May 14, 2025 at 3:18AM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">31.08 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">40                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50529"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50529-release/#disqus_thread" data-disqus-identifier="9147101 http://www.apkmirror.com/?post_type=app_release&#038;p=9147101" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                            <div style="padding-top: 4px;">
                                            <h5 title="Wikipedia 2.7.50529" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50529-release/">Wikipedia 2.7.50529</a>                                         </h5>
                                        <div class="appRowVariantTag wrapText"><a class="accent_color" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50529-release/"><svg class="icon tag-icon"><use xlink:href="#apkm-icon-tag"></use></svg>2 variants</a></div>                        </div>
                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="04/30/2025 06:01 UTC">April 30, 2025</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="04/30/2025 06:01 UTC">April 30, 2025</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50529-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50529                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="04/30/2025 06:01 UTC">April 30, 2025 at 6:01AM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">30.6 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">107                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50528"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50528-release/#disqus_thread" data-disqus-identifier="9014347 https://www.apkmirror.com/?post_type=app_release&#038;p=9014347" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                            <div style="padding-top: 4px;">
                                            <h5 title="Wikipedia 2.7.50528" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50528-release/">Wikipedia 2.7.50528</a>                                         </h5>
                                                                </div>
                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="04/16/2025 04:59 UTC">April 16, 2025</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="04/16/2025 04:59 UTC">April 16, 2025</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50528-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50528                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="04/16/2025 04:59 UTC">April 16, 2025 at 4:59AM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">30 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">54                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50524"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50524-release/#disqus_thread" data-disqus-identifier="8601614 https://www.apkmirror.com/?post_type=app_release&#038;p=8601614" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                            <div style="padding-top: 4px;">
                                            <h5 title="Wikipedia 2.7.50524" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50524-release/">Wikipedia 2.7.50524</a>                                         </h5>
                                        <div class="appRowVariantTag wrapText"><a class="accent_color" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50524-release/"><svg class="icon tag-icon"><use xlink:href="#apkm-icon-tag"></use></svg>2 variants</a></div>                        </div>
                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="04/4/2025 06:47 UTC">April 4, 2025</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="04/4/2025 06:47 UTC">April 4, 2025</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50524-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50524                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="04/4/2025 06:47 UTC">April 4, 2025 at 6:47AM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">27.81 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">129                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50527"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50527-release/#disqus_thread" data-disqus-identifier="8880166 https://www.apkmirror.com/?post_type=app_release&#038;p=8880166" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                            <div style="padding-top: 4px;">
                                            <h5 title="Wikipedia 2.7.50527" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50527-release/">Wikipedia 2.7.50527</a>                                         </h5>
                                                                </div>
                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="04/2/2025 17:38 UTC">April 2, 2025</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="04/2/2025 17:38 UTC">April 2, 2025</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50527-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50527                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="04/2/2025 17:38 UTC">April 2, 2025 at 5:38PM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">30.01 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">25                            </span>
                        </p>
                                    </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Wikipedia 2.7.50522"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F10%2F665878e0603d5_org.wikipedia.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50522-release/#disqus_thread" data-disqus-identifier="8459859 http://www.apkmirror.com/?post_type=app_release&#038;p=8459859" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                            <div style="padding-top: 4px;">
                                            <h5 title="Wikipedia 2.7.50522" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50522-release/">Wikipedia 2.7.50522</a>                                         </h5>
                                                                </div>
                                                <span style="padding-right:12px;" class="visible-xs colorLightBlack wrapText"><span style="" class="dateyear_utc" data-utcdate="02/20/2025 19:35 UTC">February 20, 2025</span></span>
                                        </div>
                                    <div style="width: 150px; text-align: right;" class="table-cell hidden-xs">
                        <span style="padding-right:12px;" class="colorLightBlack"><span style="" class="dateyear_utc" data-utcdate="02/20/2025 19:35 UTC">February 20, 2025</span></span>
                    </div>
                                    <div style="width: 60px;" class="table-cell">
                    <div class="iconsBox ">
                                                    <div class="infoIconPositioning">
                                <a class="infoLink" href="javascript:void(0);">
                                    <svg class="icon iconColor info-icon"><use xlink:href="#apkm-icon-info"></use></svg>
                                </a>
                            </div>
                                                    <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/wikimedia-foundation/wikipedia/wikipedia-2-7-50522-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <div class="infoSlide t-height">
                                <p>
                    <span class="infoSlide-name">Version:</span><span class="infoSlide-value">2.7.50522                    </span>
                </p>
                <p>
                    <span class="infoSlide-name">Uploaded:</span><span class="infoSlide-value"><span style="" class="datetime_utc" data-utcdate="02/20/2025 19:35 UTC">February 20, 2025 at 7:35PM UTC</span></span>
                </p>
                <p>
                    <span class="infoSlide-name">File size:</span><span class="infoSlide-value">27.77 MB</span>
                </p>
                                        <p>
                            <span class="infoSlide-name">Downloads:</span><span class="infoSlide-value">78                            </span>
                        </p>
                                    </div>
                </div>
                                        <div style="padding-top: 7px; padding-bottom: 7px; height: 51px;" class="table noMargins">
                                        <div class="table-row">
                                            <div class="table-cell center">
                                                <a class="fontBlack" href="/uploads/?appcategory=wikipedia">See more uploads...</a>
                                            </div>
                                        </div>
                                    </div>
                                                        </div>
                                <div class="listWidget p-relative">
                <a class="doc-anchor" name="comments" data-spy="scroll" data-target=".scroll-spy-container"></a>
                <div class="widgetHeader">Comments</div>
                <div class="addpadding ellipsisText">
                    
<div id="disqus_thread"></div>
                </div>
            </div>
    </article>
        <div class="modal fade" id="safeDownload" tabindex="-1" role="dialog" aria-labelledby="safeDownload" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title fontBlack">Safe to Download</h2>
                    </div>
                    <div class="modal-body">
                                                <h5>This APK <span style="word-break: break-all; font-style: italic;">org.wikipedia_2.7.50538-samsung-2025-06-24-50538_minAPI21(arm64-v8a,armeabi-v7a,x86,x86_64)(nodpi)_apkmirror.com.apk</span> is signed by Wikimedia Foundation and upgrades your existing app.</h5>
                        <br>
                        <h4>APK certificate fingerprints</h4>
                                                        SHA-1: <span class="wordbreak-all" style="background-color: rgb(220, 255, 166);"><a style="color: #000" href="/?s=d21a6a91aa75c937c4253770a8f7025c6c2a8319&post_type=app_release&searchtype=app">d21a6a91aa75c937c4253770a8f7025c6c2a8319</a></span><br>
                                                                SHA-256: <span class="wordbreak-all" style="background-color: rgb(220, 255, 166);"><a style="color: #000" href="/?s=f540e937419a1fe308e33531359c23ce19dfd84a6601e2ef85c217c823a7087f&post_type=app_release&searchtype=app">f540e937419a1fe308e33531359c23ce19dfd84a6601e2ef85c217c823a7087f</a></span><br>
                                                                Certificate: <span style="background-color: rgb(220, 255, 166); color: #000">CN=Wikimedia Foundation, OU=Mobile, O=Wikimedia Foundation, L=San Francisco, ST=California, C=US</span>
                                <br>                        <h5>The cryptographic signature guarantees the file is safe to install and was not tampered with in any way.</h5>
                                                    <br>
                            <h4>APK file hashes</h4>
                            MD5: <span class="wordbreak-all" style="background-color: rgb(220, 255, 166); color: #000">b337875fc971629f5d5e7b7a218c72fb</span><br>
                            SHA-1: <span class="wordbreak-all" style="background-color: rgb(220, 255, 166); color: #000">bed5f56c2095b0ac0182c30ba38dcde6b7fb2342</span><br>
                            SHA-256: <span class="wordbreak-all" style="background-color: rgb(220, 255, 166); color: #000">c93bf8bf514181fad6b1021b351f130f4475b09266cec733738ec7da48ad76c2</span>
                                                            <h5>Verify the file you downloaded is not corrupt and was not tampered with using the file hashes above.</h5>
                                                    </div>
                    <div class="modal-footer">
                        <button style="color: white;" type="button" class="accent_bg btn btn-flat" data-dismiss="modal">Got It!
                        </button>
                    </div>
                </div>
            </div>
        </div>
            <div class="modal fade" id="redirectPlayStoreModal" tabindex="-1" role="dialog" aria-labelledby="redirectPlayStoreModal" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title fontBlack">Why Can't I Download This APK?</h2>
                    </div>
                    <div class="modal-body">
                        While APKMirror strives to provide APK downloads for as many popular apps as possible, sometimes app developers ask us to
                        take their apps down and link to an official download source instead, such as the Google Play Store. Please understand that
                        we have to respect such decisions and therefore cannot offer a direct APK download for this app right now.
                    </div>
                    <div class="modal-footer">
                        <button style="color: white;" type="button" class="accent_bg btn btn-flat" data-dismiss="modal">OK
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div><!-- #primary -->

    <div id="sidebar" class="f-md-33 col-padding sidebar-widget f-stretch" role="complementary">
        <!--WP Widget Cache ********* Begin -->
<!--Cache appmanager_sidebarsocialfollowwidget-2 for 30 second(s)-->
            <div class="listWidget">
                <h5 class="widgetHeader">Follow APK Mirror</h5>
                <div class="table" style="table-layout: fixed;">
                    <div class="table-row">
                        <div class="table-cell center"><a href="https://twitter.com/apkmirror" rel="follow" target="_blank"><svg class="clickable icon icon-twitter"><use xlink:href="#apkm-icon-twitter"></use></svg></a></div>
                        <div class="table-cell center"><a href="https://mastodon.social/@apkmirror" rel="follow" target="_blank"><svg class="clickable icon icon-mastodon"><use xlink:href="#apkm-mastodon"></use></svg></a></div>
                        <div class="table-cell center"><a href="https://t.me/apkmirror_official" rel="follow" target="_blank"><svg class="clickable icon icon-telegram"><use xlink:href="#apkm-icon-telegram"></use></svg></a></div>
                        <div class="table-cell center"><a href="https://www.apkmirror.com/feed/" target="_blank"><svg class="clickable icon icon-rss-follow"><use xlink:href="#apkm-icon-rss"></use></svg></a></div>
                    </div>
                </div>
                <div class="table noMargins">
                    <div class="table-row">
                        <div class="table-cell center">
                                <p><span class="pushbullet-subscribe-widget abtn abtn-pb  alignleft" data-description="" data-channel-name="Follow APK Mirror Updates" data-image-url="" data-tag="am-1071836729" data-feed-url="" data-widget="button" data-size="large" href="#">
            <svg class="icon inline-icon ">
                <use xlink:href="#apkm-icon-pushbullet"></use>
            </svg><span>Follow APK Mirror Updates</span></span></p>
                            </div>
                    </div>
                </div>
            </div>
            <aside id="ai_widget-3" class="widget block-widget"><div class='ains ains-2 GEo  ains-has-label ains-apkm_sidebar_top ai-track' data-ai='WzIsMSwiYXBrbV9zaWRlYmFyX3RvcCIsImFwa21fZ29vZ2xlX2FkcyIsMV0='>
<span class="advertisement-text">Advertisement</span><a href="/premium" class="advertisement-text membership-text" title="APKMirror Premium" data-google-vignette="false"><span>Remove ads, dark theme, and more with <img src="/wp-content/themes/APKMirror/images/apple-touch-icon-57x57.png" alt="APKM Premium" width="18" height="18" /> Premium</span></a>
<div class="gooWidget yen google-ad-square-sidebar">
  <!-- Adsense responsive ad apkm_sidebar_top -->
  <ins class="adsbygoogle adslot NJx"
    style="display:block"
    data-ad-client="ca-pub-8776668743582988"
    data-ad-slot="3860484086"
    data-ad-format="auto"
    data-full-width-responsive="true"></ins>
  <script>
    APKM.a(103, '2');
  </script>
</div></div>
</aside><!--WP Widget Cache ********* Begin -->
<!--Cache appmanager_sidebarpopularuploadswidget-2 for 600 second(s)-->
            <div class="listWidget">
            <h5 class="widgetHeader">Popular In Last 30 Days</h5>
                    <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Google Services Framework 15"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2016%2F05%2F5729ae06ce9a7.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2016%2F05%2F5729ae06ce9a7.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2016%2F05%2F5729ae06ce9a7.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2016%2F05%2F5729ae06ce9a7.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/google-services-framework/google-services-framework-15-release/#disqus_thread" data-disqus-identifier="6469533 https://www.apkmirror.com/?post_type=app_release&#038;p=6469533" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Google Services Framework 15" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/google-services-framework/google-services-framework-15-release/">Google Services Framework 15</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">182K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/google-services-framework/google-services-framework-15-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Google Play Store 46.6.21"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2022%2F08%2F26%2F630d0e65afd4f.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2022%2F08%2F26%2F630d0e65afd4f.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2022%2F08%2F26%2F630d0e65afd4f.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2022%2F08%2F26%2F630d0e65afd4f.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/google-play-store/google-play-store-46-6-21-release/#disqus_thread" data-disqus-identifier="9651772 https://www.apkmirror.com/?post_type=app_release&#038;p=9651772" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Google Play Store 46.6.21" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/google-play-store/google-play-store-46-6-21-release/">Google Play Store 46.6.21</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">86K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/google-play-store/google-play-store-46-6-21-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Google Play Store 46.8.29"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2022%2F08%2F26%2F630d0e65afd4f.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2022%2F08%2F26%2F630d0e65afd4f.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2022%2F08%2F26%2F630d0e65afd4f.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2022%2F08%2F26%2F630d0e65afd4f.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/google-play-store/google-play-store-46-8-29-release/#disqus_thread" data-disqus-identifier="9818543 https://www.apkmirror.com/?post_type=app_release&#038;p=9818543" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Google Play Store 46.8.29" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/google-play-store/google-play-store-46-8-29-release/">Google Play Store 46.8.29</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">59K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/google-play-store/google-play-store-46-8-29-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Android Auto 14.3.6518"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/android-auto/android-auto-14-3-6518-release/#disqus_thread" data-disqus-identifier="9193984 https://www.apkmirror.com/?post_type=app_release&#038;p=9193984" title="View comments">21</a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Android Auto 14.3.6518" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/android-auto/android-auto-14-3-6518-release/">Android Auto 14.3.6518</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">55K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/android-auto/android-auto-14-3-6518-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="APKMirror Installer (Official) 1.10.3 (35-e9bfdb6)"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2020%2F03%2F5e765ec7d18ff.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2020%2F03%2F5e765ec7d18ff.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2020%2F03%2F5e765ec7d18ff.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2020%2F03%2F5e765ec7d18ff.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/apkmirror/apkmirror-installer-official/apkmirror-installer-official-1-10-3-35-e9bfdb6-release/#disqus_thread" data-disqus-identifier="9607591 https://www.apkmirror.com/?post_type=app_release&#038;p=9607591" title="View comments">1</a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="APKMirror Installer (Official) 1.10.3 (35-e9bfdb6)" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/apkmirror/apkmirror-installer-official/apkmirror-installer-official-1-10-3-35-e9bfdb6-release/">APKMirror Installer (Official) 1.10.3 (35-e9bfdb6)</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">44K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/apkmirror/apkmirror-installer-official/apkmirror-installer-official-1-10-3-35-e9bfdb6-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Google Services Framework 16"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2Fgeneric-app-icon2.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2Fgeneric-app-icon2.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2Fgeneric-app-icon2.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2Fgeneric-app-icon2.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/google-services-framework/google-services-framework-16-release/#disqus_thread" data-disqus-identifier="8671819 https://www.apkmirror.com/?post_type=app_release&#038;p=8671819" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Google Services Framework 16" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/google-services-framework/google-services-framework-16-release/">Google Services Framework 16</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">43K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/google-services-framework/google-services-framework-16-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="CapCut - Video Editor 14.5.0"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F11%2F00%2F655c2cae3b611_com.lemon.lvoverseas.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F11%2F00%2F655c2cae3b611_com.lemon.lvoverseas.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F11%2F00%2F655c2cae3b611_com.lemon.lvoverseas.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F11%2F00%2F655c2cae3b611_com.lemon.lvoverseas.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/bytedance-pte-ltd/capcut/capcut-video-editor-14-5-0-release/#disqus_thread" data-disqus-identifier="9630496 https://www.apkmirror.com/?post_type=app_release&#038;p=9630496" title="View comments">2</a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="CapCut - Video Editor 14.5.0" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/bytedance-pte-ltd/capcut/capcut-video-editor-14-5-0-release/">CapCut - Video Editor 14.5.0</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">33K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/bytedance-pte-ltd/capcut/capcut-video-editor-14-5-0-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="CapCut - Video Editor 14.4.0"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F11%2F00%2F655c2cae3b611_com.lemon.lvoverseas.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F11%2F00%2F655c2cae3b611_com.lemon.lvoverseas.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F11%2F00%2F655c2cae3b611_com.lemon.lvoverseas.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F11%2F00%2F655c2cae3b611_com.lemon.lvoverseas.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/bytedance-pte-ltd/capcut/capcut-video-editor-14-4-0-release/#disqus_thread" data-disqus-identifier="9476553 https://www.apkmirror.com/?post_type=app_release&#038;p=9476553" title="View comments">1</a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="CapCut - Video Editor 14.4.0" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/bytedance-pte-ltd/capcut/capcut-video-editor-14-4-0-release/">CapCut - Video Editor 14.4.0</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">32K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/bytedance-pte-ltd/capcut/capcut-video-editor-14-4-0-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Spotify: Music and Podcasts 9.0.58.596"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2024%2F08%2F74%2F66cb629b7e519_com.spotify.music.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2024%2F08%2F74%2F66cb629b7e519_com.spotify.music.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2024%2F08%2F74%2F66cb629b7e519_com.spotify.music.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2024%2F08%2F74%2F66cb629b7e519_com.spotify.music.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/spotify-ab/spotify-music-podcasts/spotify-music-and-podcasts-9-0-58-596-release/#disqus_thread" data-disqus-identifier="9854595 https://www.apkmirror.com/?post_type=app_release&#038;p=9854595" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Spotify: Music and Podcasts 9.0.58.596" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/spotify-ab/spotify-music-podcasts/spotify-music-and-podcasts-9-0-58-596-release/">Spotify: Music and Podcasts 9.0.58.596</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">31K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/spotify-ab/spotify-music-podcasts/spotify-music-and-podcasts-9-0-58-596-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Google Play services 25.22.34"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/google-play-services/google-play-services-25-22-34-release/#disqus_thread" data-disqus-identifier="9615513 http://www.apkmirror.com/?post_type=app_release&#038;p=9615513" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Google Play services 25.22.34" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/google-play-services/google-play-services-25-22-34-release/">Google Play services 25.22.34</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">30K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/google-play-services/google-play-services-25-22-34-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
    </div><!--WP Widget Cache ********* Begin -->
<!--Cache appmanager_sidebarpopularuploadswidget-4 for 600 second(s)-->
            <div class="listWidget">
            <h5 class="widgetHeader">Popular In Last 7 Days</h5>
                    <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Google Services Framework 16"  src="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2Fgeneric-app-icon2.png&w=32&h=32&q=100"  srcset="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2Fgeneric-app-icon2.png&w=128&h=128&q=100 128w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2Fgeneric-app-icon2.png&w=64&h=64&q=100 64w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2Fgeneric-app-icon2.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/google-services-framework/google-services-framework-16-release/#disqus_thread" data-disqus-identifier="8671819 https://www.apkmirror.com/?post_type=app_release&#038;p=8671819" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Google Services Framework 16" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/google-services-framework/google-services-framework-16-release/">Google Services Framework 16</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">43K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/google-services-framework/google-services-framework-16-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="CapCut - Video Editor 14.6.0"  src="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F11%2F00%2F655c2cae3b611_com.lemon.lvoverseas.png&w=32&h=32&q=100"  srcset="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F11%2F00%2F655c2cae3b611_com.lemon.lvoverseas.png&w=128&h=128&q=100 128w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F11%2F00%2F655c2cae3b611_com.lemon.lvoverseas.png&w=64&h=64&q=100 64w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F11%2F00%2F655c2cae3b611_com.lemon.lvoverseas.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/bytedance-pte-ltd/capcut/capcut-video-editor-14-6-0-release/#disqus_thread" data-disqus-identifier="9745055 https://www.apkmirror.com/?post_type=app_release&#038;p=9745055" title="View comments">8</a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="CapCut - Video Editor 14.6.0" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/bytedance-pte-ltd/capcut/capcut-video-editor-14-6-0-release/">CapCut - Video Editor 14.6.0</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">29K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/bytedance-pte-ltd/capcut/capcut-video-editor-14-6-0-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Android Auto 14.7.6526"  src="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=32&h=32&q=100"  srcset="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=128&h=128&q=100 128w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=64&h=64&q=100 64w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/android-auto/android-auto-14-7-6526-release/#disqus_thread" data-disqus-identifier="9816675 https://www.apkmirror.com/?post_type=app_release&#038;p=9816675" title="View comments">7</a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Android Auto 14.7.6526" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/android-auto/android-auto-14-7-6526-release/">Android Auto 14.7.6526</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">26K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/android-auto/android-auto-14-7-6526-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Google Play Store 46.9.20"  src="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2022%2F08%2F26%2F630d0e65afd4f.png&w=32&h=32&q=100"  srcset="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2022%2F08%2F26%2F630d0e65afd4f.png&w=128&h=128&q=100 128w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2022%2F08%2F26%2F630d0e65afd4f.png&w=64&h=64&q=100 64w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2022%2F08%2F26%2F630d0e65afd4f.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/google-play-store/google-play-store-46-9-20-release/#disqus_thread" data-disqus-identifier="9876109 https://www.apkmirror.com/?post_type=app_release&#038;p=9876109" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Google Play Store 46.9.20" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/google-play-store/google-play-store-46-9-20-release/">Google Play Store 46.9.20</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">21K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/google-play-store/google-play-store-46-9-20-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Android Auto 14.6.6524"  src="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=32&h=32&q=100"  srcset="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=128&h=128&q=100 128w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=64&h=64&q=100 64w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/android-auto/android-auto-14-6-6524-release/#disqus_thread" data-disqus-identifier="9664237 https://www.apkmirror.com/?post_type=app_release&#038;p=9664237" title="View comments">1</a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Android Auto 14.6.6524" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/android-auto/android-auto-14-6-6524-release/">Android Auto 14.6.6524</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">21K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/android-auto/android-auto-14-6-6524-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Android Auto 14.5.6522"  src="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=32&h=32&q=100"  srcset="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=128&h=128&q=100 128w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=64&h=64&q=100 64w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/android-auto/android-auto-14-5-6522-release/#disqus_thread" data-disqus-identifier="9612389 https://www.apkmirror.com/?post_type=app_release&#038;p=9612389" title="View comments">4</a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Android Auto 14.5.6522" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/android-auto/android-auto-14-5-6522-release/">Android Auto 14.5.6522</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">20K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/android-auto/android-auto-14-5-6522-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Google Play services 25.24.61"  src="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=32&h=32&q=100"  srcset="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=128&h=128&q=100 128w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=64&h=64&q=100 64w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/google-play-services/google-play-services-25-24-61-release/#disqus_thread" data-disqus-identifier="9839220 http://www.apkmirror.com/?post_type=app_release&#038;p=9839220" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Google Play services 25.24.61" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/google-play-services/google-play-services-25-24-61-release/">Google Play services 25.24.61</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">17K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/google-play-services/google-play-services-25-24-61-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Google Play services 25.25.31"  src="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=32&h=32&q=100"  srcset="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=128&h=128&q=100 128w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=64&h=64&q=100 64w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/google-play-services/google-play-services-25-25-31-release/#disqus_thread" data-disqus-identifier="9762666 https://www.apkmirror.com/?post_type=app_release&#038;p=9762666" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Google Play services 25.25.31" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/google-play-services/google-play-services-25-25-31-release/">Google Play services 25.25.31</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">13K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/google-play-services/google-play-services-25-25-31-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Pokémon GO (Samsung Galaxy Store) 0.367.2"  src="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F03%2F63%2F67c795a854029_com.nianticlabs.pokemongo.ares.png&w=32&h=32&q=100"  srcset="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F03%2F63%2F67c795a854029_com.nianticlabs.pokemongo.ares.png&w=128&h=128&q=100 128w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F03%2F63%2F67c795a854029_com.nianticlabs.pokemongo.ares.png&w=64&h=64&q=100 64w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F03%2F63%2F67c795a854029_com.nianticlabs.pokemongo.ares.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/niantic-inc/pokemon-go-samsung-galaxy-apps-version/pokemon-go-samsung-galaxy-store-0-367-2-release/#disqus_thread" data-disqus-identifier="9860459 https://www.apkmirror.com/?post_type=app_release&#038;p=9860459" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Pokémon GO (Samsung Galaxy Store) 0.367.2" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/niantic-inc/pokemon-go-samsung-galaxy-apps-version/pokemon-go-samsung-galaxy-store-0-367-2-release/">Pokémon GO (Samsung Galaxy Store) 0.367.2</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">8.4K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/niantic-inc/pokemon-go-samsung-galaxy-apps-version/pokemon-go-samsung-galaxy-store-0-367-2-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Spotify: Music and Podcasts 9.0.60.588"  src="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2024%2F08%2F74%2F66cb629b7e519_com.spotify.music.png&w=32&h=32&q=100"  srcset="https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2024%2F08%2F74%2F66cb629b7e519_com.spotify.music.png&w=128&h=128&q=100 128w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2024%2F08%2F74%2F66cb629b7e519_com.spotify.music.png&w=64&h=64&q=100 64w,
        https://www.apkmirror.com/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2024%2F08%2F74%2F66cb629b7e519_com.spotify.music.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/spotify-ab/spotify-music-podcasts/spotify-music-and-podcasts-9-0-60-588-release/#disqus_thread" data-disqus-identifier="9883802 https://www.apkmirror.com/?post_type=app_release&#038;p=9883802" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Spotify: Music and Podcasts 9.0.60.588" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/spotify-ab/spotify-music-podcasts/spotify-music-and-podcasts-9-0-60-588-release/">Spotify: Music and Podcasts 9.0.60.588</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">6.6K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/spotify-ab/spotify-music-podcasts/spotify-music-and-podcasts-9-0-60-588-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
    </div><!--WP Widget Cache ********* Begin -->
<!--Cache appmanager_sidebarpopularuploadswidget-3 for 600 second(s)-->
            <div class="listWidget">
            <h5 class="widgetHeader">Popular In Last 24 Hours</h5>
                    <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Android Auto 14.6.6524"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/android-auto/android-auto-14-6-6524-release/#disqus_thread" data-disqus-identifier="9664237 https://www.apkmirror.com/?post_type=app_release&#038;p=9664237" title="View comments">1</a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Android Auto 14.6.6524" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/android-auto/android-auto-14-6-6524-release/">Android Auto 14.6.6524</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">21K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/android-auto/android-auto-14-6-6524-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Google Play Store 47.0.13"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2022%2F08%2F26%2F630d0e65afd4f.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2022%2F08%2F26%2F630d0e65afd4f.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2022%2F08%2F26%2F630d0e65afd4f.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2022%2F08%2F26%2F630d0e65afd4f.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/google-play-store/google-play-store-47-0-13-release/#disqus_thread" data-disqus-identifier="9918319 https://www.apkmirror.com/?post_type=app_release&#038;p=9918319" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Google Play Store 47.0.13" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/google-play-store/google-play-store-47-0-13-release/">Google Play Store 47.0.13</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">5.3K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/google-play-store/google-play-store-47-0-13-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Google Play Services for AR 1.50.2516700"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3fa1586cbe8.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3fa1586cbe8.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3fa1586cbe8.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3fa1586cbe8.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/arcore/google-play-services-for-ar-1-50-2516700-release/#disqus_thread" data-disqus-identifier="9865013 http://www.apkmirror.com/?post_type=app_release&#038;p=9865013" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Google Play Services for AR 1.50.2516700" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/arcore/google-play-services-for-ar-1-50-2516700-release/">Google Play Services for AR 1.50.2516700</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">2.5K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/arcore/google-play-services-for-ar-1-50-2516700-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="WhatsApp Messenger ********** beta"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F06%2F5cf95ae8c3143.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F06%2F5cf95ae8c3143.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F06%2F5cf95ae8c3143.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F06%2F5cf95ae8c3143.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/whatsapp-inc/whatsapp/whatsapp-messenger-2-25-20-13-release/#disqus_thread" data-disqus-identifier="9930152 https://www.apkmirror.com/?post_type=app_release&#038;p=9930152" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="WhatsApp Messenger ********** beta" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/whatsapp-inc/whatsapp/whatsapp-messenger-2-25-20-13-release/">WhatsApp Messenger ********** beta</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">1.7K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/whatsapp-inc/whatsapp/whatsapp-messenger-2-25-20-13-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Google Play services 25.25.32"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/google-play-services/google-play-services-25-25-32-release/#disqus_thread" data-disqus-identifier="9918521 http://www.apkmirror.com/?post_type=app_release&#038;p=9918521" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Google Play services 25.25.32" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/google-play-services/google-play-services-25-25-32-release/">Google Play services 25.25.32</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">1.6K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/google-play-services/google-play-services-25-25-32-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Google Play services 25.26.31 beta"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2021%2F07%2F80%2F60ec9b7cad6dc.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/google-play-services/google-play-services-25-26-31-release/#disqus_thread" data-disqus-identifier="9838834 https://www.apkmirror.com/?post_type=app_release&#038;p=9838834" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Google Play services 25.26.31 beta" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/google-play-services/google-play-services-25-26-31-release/">Google Play services 25.26.31 beta</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">1.6K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/google-play-services/google-play-services-25-26-31-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Android Auto 14.8.1527 beta"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2019%2F07%2F5d3f3d7cc25a9.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/android-auto/android-auto-14-8-1527-release/#disqus_thread" data-disqus-identifier="9926934 https://www.apkmirror.com/?post_type=app_release&#038;p=9926934" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Android Auto 14.8.1527 beta" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/android-auto/android-auto-14-8-1527-release/">Android Auto 14.8.1527 beta</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">1.6K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/android-auto/android-auto-14-8-1527-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Android System WebView 138.0.7204.63"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F02%2F68%2F63eac39911b5d.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F02%2F68%2F63eac39911b5d.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F02%2F68%2F63eac39911b5d.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F02%2F68%2F63eac39911b5d.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/android-system-webview/android-system-webview-138-0-7204-63-release/#disqus_thread" data-disqus-identifier="9853261 http://www.apkmirror.com/?post_type=app_release&#038;p=9853261" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Android System WebView 138.0.7204.63" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/android-system-webview/android-system-webview-138-0-7204-63-release/">Android System WebView 138.0.7204.63</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">1.6K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/android-system-webview/android-system-webview-138-0-7204-63-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Google App 16.25.46"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F05%2F44%2F68275a105b294_com.google.android.googlequicksearchbox.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F05%2F44%2F68275a105b294_com.google.android.googlequicksearchbox.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F05%2F44%2F68275a105b294_com.google.android.googlequicksearchbox.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F05%2F44%2F68275a105b294_com.google.android.googlequicksearchbox.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/google-inc/google-search/google-app-16-25-46-release/#disqus_thread" data-disqus-identifier="9865434 http://www.apkmirror.com/?post_type=app_release&#038;p=9865434" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Google App 16.25.46" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/google-inc/google-search/google-app-16-25-46-release/">Google App 16.25.46</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">1.4K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/google-inc/google-search/google-app-16-25-46-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Facebook 520.*********"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F09%2F52%2F6513b13ad4d39_com.facebook.katana.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F09%2F52%2F6513b13ad4d39_com.facebook.katana.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F09%2F52%2F6513b13ad4d39_com.facebook.katana.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F09%2F52%2F6513b13ad4d39_com.facebook.katana.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/facebook-2/facebook/facebook-520-0-0-44-99-release/#disqus_thread" data-disqus-identifier="9873728 https://www.apkmirror.com/?post_type=app_release&#038;p=9873728" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Facebook 520.*********" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/facebook-2/facebook/facebook-520-0-0-44-99-release/">Facebook 520.*********</a>                                         </h5>
                                                        </div>
                                    <div style="width: 50px; text-align: right;" class="table-cell">
                        <span style="padding-right:12px;" class="colorLightBlack">1.1K</span>
                    </div>
                                    <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/facebook-2/facebook/facebook-520-0-0-44-99-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
    </div><!--WP Widget Cache ********* Begin -->
<!--Cache appmanager_sidebaruploadswidget-2 for 600 second(s)-->
            <div class="listWidget">
                <h5 class="widgetHeader">Latest Uploads</h5>
                        <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Dice Dreams™️ 1.93.1.27576"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F12%2F42%2F65970cfe32afe_com.superplaystudios.dicedreams.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F12%2F42%2F65970cfe32afe_com.superplaystudios.dicedreams.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F12%2F42%2F65970cfe32afe_com.superplaystudios.dicedreams.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F12%2F42%2F65970cfe32afe_com.superplaystudios.dicedreams.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/superplay/dice-dreams%ef%b8%8f/dice-dreams%ef%b8%8f-1-93-1-27576-release/#disqus_thread" data-disqus-identifier="9939134 http://www.apkmirror.com/?post_type=app_release&#038;p=9939134" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Dice Dreams™️ 1.93.1.27576" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/superplay/dice-dreams%ef%b8%8f/dice-dreams%ef%b8%8f-1-93-1-27576-release/">Dice Dreams™️ 1.93.1.27576</a>                                         </h5>
                                                        </div>
                                <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/superplay/dice-dreams%ef%b8%8f/dice-dreams%ef%b8%8f-1-93-1-27576-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Grok - AI Assistant 0.6.5"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F03%2F48%2F67c3bbfbeed1a_ai.x.grok.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F03%2F48%2F67c3bbfbeed1a_ai.x.grok.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F03%2F48%2F67c3bbfbeed1a_ai.x.grok.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F03%2F48%2F67c3bbfbeed1a_ai.x.grok.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/xai/grok/grok-ai-assistant-0-6-5-release/#disqus_thread" data-disqus-identifier="9939098 http://www.apkmirror.com/?post_type=app_release&#038;p=9939098" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Grok - AI Assistant 0.6.5" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/xai/grok/grok-ai-assistant-0-6-5-release/">Grok - AI Assistant 0.6.5</a>                                         </h5>
                                                        </div>
                                <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/xai/grok/grok-ai-assistant-0-6-5-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="GoodShort - Movies &amp; Dramas 2.3.9.2039"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2024%2F08%2F15%2F66ba78da85769_com.newreading.goodreels.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2024%2F08%2F15%2F66ba78da85769_com.newreading.goodreels.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2024%2F08%2F15%2F66ba78da85769_com.newreading.goodreels.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2024%2F08%2F15%2F66ba78da85769_com.newreading.goodreels.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/goodnovel/goodshort-movies-dramas/goodshort-movies-dramas-2-3-9-2039-release/#disqus_thread" data-disqus-identifier="9939109 http://www.apkmirror.com/?post_type=app_release&#038;p=9939109" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="GoodShort - Movies &amp; Dramas 2.3.9.2039" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/goodnovel/goodshort-movies-dramas/goodshort-movies-dramas-2-3-9-2039-release/">GoodShort - Movies &amp; Dramas 2.3.9.2039</a>                                         </h5>
                                                        </div>
                                <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/goodnovel/goodshort-movies-dramas/goodshort-movies-dramas-2-3-9-2039-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Rail Europe 3.0.4"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2024%2F02%2F14%2F65c4249375690_com.loco2.loco2.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2024%2F02%2F14%2F65c4249375690_com.loco2.loco2.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2024%2F02%2F14%2F65c4249375690_com.loco2.loco2.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2024%2F02%2F14%2F65c4249375690_com.loco2.loco2.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/rail-europe-formerly-loco2/rail-europe/rail-europe-3-0-4-release/#disqus_thread" data-disqus-identifier="9939096 http://www.apkmirror.com/?post_type=app_release&#038;p=9939096" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Rail Europe 3.0.4" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/rail-europe-formerly-loco2/rail-europe/rail-europe-3-0-4-release/">Rail Europe 3.0.4</a>                                         </h5>
                                                        </div>
                                <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/rail-europe-formerly-loco2/rail-europe/rail-europe-3-0-4-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="m-zaba 3.25.23"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2020%2F08%2F14%2F5f3da0b089441.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2020%2F08%2F14%2F5f3da0b089441.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2020%2F08%2F14%2F5f3da0b089441.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2020%2F08%2F14%2F5f3da0b089441.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/zagrebacka-banka-d-d/m-zaba/m-zaba-3-25-23-release/#disqus_thread" data-disqus-identifier="9939076 http://www.apkmirror.com/?post_type=app_release&#038;p=9939076" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="m-zaba 3.25.23" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/zagrebacka-banka-d-d/m-zaba/m-zaba-3-25-23-release/">m-zaba 3.25.23</a>                                         </h5>
                                                        </div>
                                <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/zagrebacka-banka-d-d/m-zaba/m-zaba-3-25-23-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Dolap 4.105.0"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F09%2F40%2F65009006e13b9_com.dolap.android.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F09%2F40%2F65009006e13b9_com.dolap.android.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F09%2F40%2F65009006e13b9_com.dolap.android.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F09%2F40%2F65009006e13b9_com.dolap.android.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/trendyol/dolap/dolap-4-105-0-release/#disqus_thread" data-disqus-identifier="9938942 http://www.apkmirror.com/?post_type=app_release&#038;p=9938942" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Dolap 4.105.0" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/trendyol/dolap/dolap-4-105-0-release/">Dolap 4.105.0</a>                                         </h5>
                                                        </div>
                                <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/trendyol/dolap/dolap-4-105-0-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Depop - Buy &amp; Sell Clothes App 2.349.1"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F03%2F89%2F67e464b884bf1_com.depop.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F03%2F89%2F67e464b884bf1_com.depop.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F03%2F89%2F67e464b884bf1_com.depop.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F03%2F89%2F67e464b884bf1_com.depop.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/depop/depop-buy-sell-clothes-app/depop-buy-sell-clothes-app-2-349-1-release/#disqus_thread" data-disqus-identifier="9938954 http://www.apkmirror.com/?post_type=app_release&#038;p=9938954" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Depop - Buy &amp; Sell Clothes App 2.349.1" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/depop/depop-buy-sell-clothes-app/depop-buy-sell-clothes-app-2-349-1-release/">Depop - Buy &amp; Sell Clothes App 2.349.1</a>                                         </h5>
                                                        </div>
                                <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/depop/depop-buy-sell-clothes-app/depop-buy-sell-clothes-app-2-349-1-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="MyInvestor · Banco &amp; Inversión 3.91.1"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F06%2F38%2F683e28239ce23_com.myinvestor.es.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F06%2F38%2F683e28239ce23_com.myinvestor.es.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F06%2F38%2F683e28239ce23_com.myinvestor.es.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2025%2F06%2F38%2F683e28239ce23_com.myinvestor.es.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/myinvestor-banco-s-a/myinvestor-%c2%b7-banco-inversion/myinvestor-%c2%b7-banco-inversion-3-91-1-release/#disqus_thread" data-disqus-identifier="9938914 http://www.apkmirror.com/?post_type=app_release&#038;p=9938914" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="MyInvestor · Banco &amp; Inversión 3.91.1" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/myinvestor-banco-s-a/myinvestor-%c2%b7-banco-inversion/myinvestor-%c2%b7-banco-inversion-3-91-1-release/">MyInvestor · Banco &amp; Inversión 3.91.1</a>                                         </h5>
                                                        </div>
                                <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/myinvestor-banco-s-a/myinvestor-%c2%b7-banco-inversion/myinvestor-%c2%b7-banco-inversion-3-91-1-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Webcams 2.0.33"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F10%2F03%2F6529bb52e6a24_com.earthcam.webcams.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F10%2F03%2F6529bb52e6a24_com.earthcam.webcams.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F10%2F03%2F6529bb52e6a24_com.earthcam.webcams.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2023%2F10%2F03%2F6529bb52e6a24_com.earthcam.webcams.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/earthcam-inc/webcams/webcams-2-0-33-release/#disqus_thread" data-disqus-identifier="9938905 http://www.apkmirror.com/?post_type=app_release&#038;p=9938905" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Webcams 2.0.33" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/earthcam-inc/webcams/webcams-2-0-33-release/">Webcams 2.0.33</a>                                         </h5>
                                                        </div>
                                <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/earthcam-inc/webcams/webcams-2-0-33-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
            <div>
        <div class="appRow">
            <div class="table-row">
                                    <div style="width: 56px;" class="table-cell">
                        <div class="bubble-wrap p-relative">
                            <img class="ellipsisText" style="width:32px; height:32px;" alt="Moje ING mobile 4.19.2"  src="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2020%2F06%2F92%2F5ef127f663bf9.png&w=32&h=32&q=100"  srcset="/wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2020%2F06%2F92%2F5ef127f663bf9.png&w=128&h=128&q=100 128w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2020%2F06%2F92%2F5ef127f663bf9.png&w=64&h=64&q=100 64w,
        /wp-content/themes/APKMirror/ap_resize/ap_resize.php?src=https%3A%2F%2Fdownloadr2.apkmirror.com%2Fwp-content%2Fuploads%2F2020%2F06%2F92%2F5ef127f663bf9.png&w=32&h=32&q=100 32w" sizes="32px"  loading="lazy"><span class="bubble "><a href="/apk/ing-bank-slaski-s-a/moje-ing-mobile/moje-ing-mobile-4-19-2-release/#disqus_thread" data-disqus-identifier="9938899 http://www.apkmirror.com/?post_type=app_release&#038;p=9938899" title="View comments"></a></span>                        </div>
                    </div>
                                    <div class="table-cell">
                                        <h5 title="Moje ING mobile 4.19.2" class="appRowTitle wrapText marginZero block-on-mobile">
                    <a class="fontBlack" href="/apk/ing-bank-slaski-s-a/moje-ing-mobile/moje-ing-mobile-4-19-2-release/">Moje ING mobile 4.19.2</a>                                         </h5>
                                                        </div>
                                <div style="width: 22px;" class="table-cell">
                    <div class="iconsBox one-icon">
                                                <div class="downloadIconPositioning">
                            <a class="downloadLink" href="/apk/ing-bank-slaski-s-a/moje-ing-mobile/moje-ing-mobile-4-19-2-release/" data-google-interstitial="false">
                                <svg class="icon iconColor download-icon"><use xlink:href="#apkm-icon-download"></use></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>
                    <div style="padding-top: 7px; padding-bottom: 7px; height: 50px;" class="table noMargins">
                    <div class="table-row" >
                        <div class="table-cell center">
                            <a class="fontBlack" href="/uploads/">See more uploads...</a>
                        </div>
                    </div>
                </div>
            </div>
                </div><!-- Sidebar -->
</div></div><script>
    if (typeof ai_ignore_iframe_ids !== "undefined" && ai_ignore_iframe_ids.constructor === Array) {
        ai_ignore_iframe_ids.push('upload-apk-*');
        ai_ignore_iframe_ids.push('dsq-app*');
    } else {
        var ai_ignore_iframe_ids = ['upload-apk-*', 'dsq-app*'];
    }
</script>
<div class="footer" role="contentinfo">
    <div class="container">
        <div>
                    <div id="footerwidget" class="sidebar-footer" role="complementary">
            </div><!-- #secondary -->
        </div>
            <div class="copyRight">
                    Notice a bug? <a target="_blank" href="https://github.com/illogical-robot/apkmirror-public/issues" rel="nofollow">Let us know here.</a><br>
            261 queries | 0.928s | hive | am<span id="apkm-ad-group"></span>
                    <span class="hidden-xs inlineBlock"> | </span><br class="visible-xs-inline hidden-md hidden-sm">
            <a href="/apkmirror-com-privacy-policy/">Privacy Policy | </a>
            <span id="apkm_consent_holder"></span>
            <a href="/dmca-copyright-infringement-notification/">DMCA Disclaimer | </a>
            <a href="/contact-us/">Contact Us</a>
            <span class="hidden-xs inlineBlock"> | </span><br class="visible-xs-inline hidden-md hidden-sm">
            Android is a trademark of Google Inc
            <span class="hidden-xs inlineBlock"> | </span><br class="visible-xs-inline hidden-md hidden-sm">
                    &copy; Illogical Robot LLC, 2014-2025            </div>
        </div>
</div>
</div><script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/*"},{"not":{"href_matches":["\/wordpress\/wp-*.php","\/wordpress\/wp-admin\/*","\/wp-content\/uploads\/*","\/wp-content\/*","\/wp-content\/plugins\/*","\/wp-content\/themes\/APKMirror\/*","\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>
<script type="text/javascript">
  (function(c) {
    var script = document.createElement("script");
    script.src = "https://js.memberful.com/embed.js";
    script.onload = function() { Memberful.setup(c) };
    document.head.appendChild(script);
  })({
    site: ["https:\/\/apkmirror.memberful.com"]  });
</script>
<svg aria-hidden="true" style="position: absolute; width: 0; height: 0; overflow: hidden;" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
<symbol id="apkm-email-search" viewBox="0 0 24 24">
<path d="M16.729 14.404c-0.638 0.905-1.42 1.687-2.325 2.326-0.168 0.119-0.28 0.301-0.306 0.499s0.032 0.394 0.163 0.552l4.758 5.61c0.322 0.387 0.791 0.61 1.282 0.61 0.442 0 0.863-0.178 1.186-0.5l2.011-2.011c0.343-0.343 0.52-0.79 0.498-1.26s-0.238-0.9-0.611-1.211l-5.605-4.755c-0.133-0.111-0.297-0.171-0.463-0.171-0.232 0-0.453 0.117-0.59 0.312z"></path>
<path d="M8.78 17.56c4.849 0 8.78-3.931 8.78-8.78s-3.931-8.78-8.78-8.78c-4.849 0-8.78 3.931-8.78 8.78s3.931 8.78 8.78 8.78zM8.78 2.195c3.631 0 6.585 2.954 6.585 6.585 0 1.665-0.622 3.187-1.645 4.347-0.608 0.69-1.357 1.251-2.203 1.639-0.834 0.383-1.761 0.598-2.737 0.598s-1.903-0.215-2.737-0.598c-0.845-0.388-1.595-0.95-2.203-1.639-1.023-1.161-1.645-2.682-1.645-4.347-0-3.631 2.954-6.585 6.585-6.585z"></path>
<path d="M9.947 10.129h-0.032q-0.474 1.544-1.862 1.544-0.889 0-1.434-0.655-0.538-0.655-0.538-1.765 0-1.466 0.746-2.433 0.753-0.973 1.972-0.973 0.467 0 0.83 0.24 0.37 0.24 0.474 0.603h0.026q0.013-0.182 0.065-0.746h0.811q-0.305 3.574-0.305 3.639 0 1.343 0.772 1.343 0.701 0 1.161-0.759 0.467-0.759 0.467-1.966 0-1.784-1.129-2.913t-3.127-1.129q-1.927 0-3.185 1.349t-1.259 3.373q0 1.992 1.194 3.218 1.2 1.22 3.231 1.22 1.602 0 2.738-0.532v0.804q-1.122 0.48-2.802 0.48-2.361 0-3.808-1.421-1.44-1.427-1.44-3.73 0-2.374 1.512-3.938 1.518-1.563 3.873-1.563 2.206 0 3.652 1.31t1.447 3.419q0 1.544-0.765 2.537-0.759 0.986-1.855 0.986-1.421 0-1.427-1.544zM8.786 6.6q-0.804 0-1.304 0.765-0.493 0.765-0.493 1.901 0 0.765 0.331 1.207 0.337 0.441 0.895 0.441 0.804 0 1.271-0.798 0.474-0.804 0.474-2.128 0-1.388-1.174-1.388z"></path>
</symbol>
<symbol id="apkm-person-search" viewBox="0 0 24 24">
<path d="M8.78 14.737c1.588 0 3.084-0.62 4.21-1.747 0.212-0.212 0.406-0.437 0.581-0.673l-0.171-0.929c0-0.365-0.296-0.661-0.661-0.661l-1.61-0.328c-0.315-0.393-0.597-0.836-0.788-1.156 0.548-0.36 0.98-0.901 1.221-1.547 0.402-0.025 0.721-0.358 0.721-0.767 0-0.339-0.22-0.626-0.524-0.729-0.147-1.622-1.425-2.889-2.98-2.889s-2.833 1.268-2.98 2.889c-0.304 0.103-0.524 0.39-0.524 0.729 0 0.409 0.319 0.742 0.721 0.767 0.24 0.646 0.673 1.188 1.221 1.547-0.19 0.319-0.471 0.76-0.785 1.152l-1.613 0.332c-0.365 0-0.661 0.296-0.661 0.661l-0.17 0.928c1.086 1.468 2.829 2.422 4.791 2.422z"></path>
<path d="M16.729 14.404c-0.638 0.905-1.42 1.687-2.325 2.326-0.168 0.119-0.28 0.301-0.306 0.499s0.032 0.394 0.163 0.552l4.758 5.61c0.322 0.387 0.791 0.61 1.282 0.61 0.442 0 0.863-0.178 1.186-0.5l2.011-2.011c0.343-0.343 0.52-0.79 0.498-1.26s-0.238-0.9-0.611-1.211l-5.605-4.755c-0.133-0.111-0.297-0.171-0.463-0.171-0.232 0-0.453 0.117-0.59 0.312z"></path>
<path d="M8.78 17.56c4.849 0 8.78-3.931 8.78-8.78s-3.931-8.78-8.78-8.78c-4.849 0-8.78 3.931-8.78 8.78s3.931 8.78 8.78 8.78zM8.78 2.195c3.631 0 6.585 2.954 6.585 6.585 0 1.665-0.622 3.187-1.645 4.347-0.608 0.69-1.357 1.251-2.203 1.639-0.834 0.383-1.761 0.598-2.737 0.598s-1.903-0.215-2.737-0.598c-0.845-0.388-1.595-0.95-2.203-1.639-1.023-1.161-1.645-2.682-1.645-4.347-0-3.631 2.954-6.585 6.585-6.585z"></path>
</symbol>
<symbol id="apkm-icon-github-checkmark" viewBox="0 0 24 24">
<path d="M18.226 0.948c-2.865 0-5.249 2.095-5.703 4.834 0.462 0.021 0.893 0.059 1.285 0.111 0.394-2.081 2.225-3.659 4.418-3.659 0.823 0 1.628 0.224 2.327 0.648 0.304 0.184 0.699 0.087 0.883-0.217s0.087-0.699-0.217-0.882c-0.9-0.546-1.935-0.834-2.993-0.834zM13.808 5.893c-0.051 0.272-0.080 0.552-0.080 0.838 0 2.48 2.018 4.498 4.498 4.498 1.403 0 2.658-0.646 3.484-1.657-0.148-0.193-0.303-0.383-0.471-0.565 0.119-0.292 0.163-1.456-0.075-2.905l-2.725 2.725c-0.121 0.121-0.284 0.188-0.454 0.188-0.006 0-0.012-0-0.018-0-0.177-0.005-0.343-0.083-0.461-0.214l-1.673-1.872c-0.236-0.265-0.214-0.671 0.051-0.907s0.671-0.214 0.907 0.051l1.22 1.365 2.823-2.823c-0.109-0.387-0.238-0.78-0.393-1.17 0 0-2.276 0.25-5.72 2.614-0.248-0.069-0.568-0.121-0.913-0.166zM20.834 4.615c0.145 0.513 0.253 1.015 0.33 1.487l1.783-1.783c0.251-0.251 0.251-0.658 0-0.909s-0.658-0.251-0.909 0l-1.205 1.205zM21.71 9.572c0.272 0.356 0.511 0.731 0.71 1.134 0.984-1.037 1.589-2.436 1.589-3.975 0-0.245-0.016-0.492-0.046-0.734-0.045-0.352-0.366-0.602-0.718-0.557s-0.601 0.366-0.557 0.718c0.024 0.189 0.036 0.382 0.036 0.573 0 1.077-0.381 2.066-1.014 2.841zM22.42 10.706c-1.055 1.112-2.544 1.808-4.194 1.808-3.189 0-5.783-2.594-5.783-5.783 0-0.323 0.028-0.64 0.079-0.949-0.317-0.014-0.641-0.022-0.968-0.022-1.223 0-2.445 0.098-3.167 0.299-3.445-2.364-5.721-2.614-5.721-2.614-0.992 2.497-0.982 5.104-0.797 5.563-1.166 1.267-1.879 2.789-1.879 4.865 0 9.032 7.495 9.176 9.385 9.176l2.179 0.003 2.177-0.003c1.893 0 9.385-0.145 9.385-9.176 0-1.23-0.251-2.264-0.697-3.167zM7.428 12.925c1.216 0.010 2.61 0.197 4.091 0.197h0.070c2.962 0 5.575-0.749 7.119 0.667 0.924 0.85 1.313 1.873 1.313 2.977 0 4.608-3.691 5.172-8.432 5.172h-0.070c-4.74 0-8.432-0.564-8.432-5.172 0-1.105 0.39-2.128 1.316-2.977 0.771-0.708 1.81-0.875 3.026-0.865zM7.558 14.893c-0.902 0-1.634 1.014-1.634 2.261 0 1.249 0.731 2.264 1.634 2.264 0.904 0 1.635-1.014 1.635-2.264 0-1.247-0.732-2.261-1.635-2.261zM15.551 14.893c-0.902 0-1.634 1.014-1.634 2.261 0 1.249 0.732 2.264 1.634 2.264s1.634-1.014 1.634-2.264c-0.001-1.247-0.731-2.261-1.634-2.261z"></path>
</symbol>
<symbol id="apkm-icon-github" viewBox="0 0 24 24">
<path d="M16.148 13.707c-0.937 0-1.696 1.053-1.696 2.346 0 1.297 0.759 2.349 1.696 2.349s1.696-1.053 1.696-2.349c-0.001-1.294-0.759-2.346-1.696-2.346zM22.051 7.599c0.194-0.476 0.202-3.181-0.828-5.773 0 0-2.362 0.26-5.936 2.713-0.75-0.209-2.019-0.31-3.286-0.31-1.269 0-2.537 0.102-3.287 0.31-3.575-2.453-5.937-2.713-5.937-2.713-1.029 2.592-1.019 5.297-0.827 5.773-1.21 1.315-1.95 2.894-1.95 5.049 0 9.373 7.778 9.523 9.739 9.523l2.262 0.003 2.259-0.003c1.964 0 9.739-0.15 9.739-9.523 0-2.155-0.738-3.734-1.949-5.049zM12.037 21.019h-0.073c-4.919 0-8.751-0.586-8.751-5.367 0-1.146 0.404-2.208 1.366-3.090 1.6-1.47 4.311-0.693 7.385-0.693h0.073c3.074 0 5.786-0.777 7.388 0.693 0.959 0.882 1.363 1.943 1.363 3.090 0 4.782-3.831 5.367-8.751 5.367zM7.854 13.707c-0.937 0-1.696 1.053-1.696 2.346 0 1.297 0.759 2.349 1.696 2.349 0.938 0 1.697-1.053 1.697-2.349 0-1.294-0.759-2.346-1.697-2.346z"></path>
</symbol>
<symbol id="apkm-mastodon" viewBox="0 0 22 24">
<path d="M22.39 5.296c-0.346-2.546-2.59-4.553-5.249-4.941-0.449-0.066-2.149-0.305-6.086-0.305h-0.029c-3.939 0-4.784 0.239-5.232 0.305-2.585 0.378-4.946 2.181-5.519 4.757-0.276 1.269-0.305 2.675-0.254 3.966 0.073 1.85 0.087 3.697 0.257 5.54 0.117 1.224 0.322 2.438 0.613 3.634 0.544 2.208 2.749 4.045 4.908 4.795 2.312 0.782 4.798 0.911 7.181 0.375 0.262-0.060 0.521-0.13 0.778-0.21 0.578-0.182 1.257-0.386 1.755-0.743 0.007-0.005 0.012-0.011 0.016-0.019s0.006-0.016 0.006-0.024v-1.786c-0-0.008-0.002-0.016-0.006-0.023s-0.009-0.013-0.015-0.018c-0.006-0.005-0.014-0.008-0.021-0.010s-0.016-0.002-0.024 0c-1.526 0.361-3.090 0.541-4.66 0.538-2.701 0-3.427-1.268-3.635-1.795-0.167-0.456-0.273-0.932-0.316-1.415-0-0.008 0.001-0.016 0.004-0.024s0.008-0.014 0.015-0.019c0.006-0.005 0.014-0.009 0.022-0.010s0.016-0.002 0.024 0c1.501 0.358 3.039 0.539 4.583 0.539 0.371 0 0.742 0 1.113-0.010 1.553-0.043 3.19-0.122 4.717-0.417 0.038-0.008 0.076-0.014 0.109-0.024 2.41-0.458 4.703-1.894 4.936-5.533 0.009-0.143 0.030-1.5 0.030-1.649 0.001-0.505 0.164-3.583-0.024-5.474zM18.681 14.374h-2.534v-6.139c0-1.292-0.544-1.952-1.652-1.952-1.217 0-1.827 0.78-1.827 2.32v3.36h-2.519v-3.36c0-1.54-0.611-2.32-1.828-2.32-1.101 0-1.651 0.659-1.652 1.952v6.139h-2.532v-6.325c0-1.292 0.334-2.319 1.001-3.080 0.688-0.759 1.591-1.149 2.712-1.149 1.297 0 2.277 0.493 2.93 1.479l0.631 1.047 0.632-1.047c0.653-0.986 1.633-1.479 2.928-1.479 1.119 0 2.022 0.39 2.713 1.149 0.667 0.76 1.001 1.787 1.001 3.080l-0.002 6.325z"></path>
</symbol>
<symbol id="apkm-icon-playstore-checkmark" viewBox="0 0 24 24">
<path d="M9.125 0.425c-1.125 0-2.1 0.95-2.1 2.175v2.8h-7.025v15.625c0 1.4 1.2 2.55 2.55 2.55h18.875c1.4 0 2.55-1.15 2.55-2.55v-15.625h-7.175v-2.8c0-1.225-0.95-2.175-2.075-2.175h-5.6zM9.55 2.8h4.95v2.6h-4.95v-2.6zM16.69 8.925l2.213 2.094-8.857 9.36-4.876-4.614 2.094-2.213 2.663 2.519 6.763-7.147z"></path>
</symbol>
<symbol id="apkm-icon-lock" viewBox="0 0 24 24">
<path d="M18.571 21.279v-10.986h-13.142v10.986h13.142zM18.571 8.136q0.873 0 1.54 0.642t0.667 1.514v10.986q0 0.873-0.667 1.514t-1.54 0.642h-13.142q-0.873 0-1.54-0.642t-0.667-1.514v-10.986q0-0.873 0.667-1.514t1.54-0.642h9.96v-2.208q0-1.386-1.001-2.387t-2.387-1.001-2.387 1.001-1.001 2.387h-2.105q0-2.259 1.617-3.876t3.876-1.617 3.876 1.617 1.617 3.876v2.208h1.078zM12 17.993q-0.873 0-1.54-0.667t-0.667-1.54 0.667-1.54 1.54-0.667 1.54 0.667 0.667 1.54-0.667 1.54-1.54 0.667z"></path>
</symbol>
<symbol id="apkm-logout" viewBox="0 0 24 24">
<path d="M3.984 5.016v13.969h8.016v2.016h-8.016q-0.797 0-1.383-0.609t-0.586-1.406v-13.969q0-0.797 0.586-1.406t1.383-0.609h8.016v2.016h-8.016zM17.016 6.984l4.969 5.016-4.969 5.016-1.406-1.453 2.578-2.578h-10.172v-1.969h10.172l-2.578-2.625z"></path>
</symbol>
<symbol id="apkm-login" viewBox="0 0 24 24">
<path d="M11.016 6.984l-1.406 1.406 2.578 2.625h-10.172v1.969h10.172l-2.578 2.625 1.406 1.406 4.969-5.016zM20.016 18.984h-8.016v2.016h8.016q0.797 0 1.383-0.586t0.586-1.43v-13.969q0-0.844-0.586-1.43t-1.383-0.586h-8.016v2.016h8.016v13.969z"></path>
</symbol>
<symbol id="apkm-checkmark" viewBox="0 0 24 24">
<path fill="#fff" style="fill: var(--color1, #fff)" d="M16.706 0.045l5.209-0.041-9.141 23.987-5.213 0.005z"></path>
<path fill="#f88b18" style="fill: var(--color2, #f88b18)" d="M2.085 9.972h4.859l1.55 4.057-1.968 8.334z"></path>
</symbol>
<symbol id="apkm-theme-light" viewBox="0 0 24 24">
<path d="M12 8.016q1.641 0 2.813 1.172t1.172 2.813-1.172 2.813-2.813 1.172-2.813-1.172-1.172-2.813 1.172-2.813 2.813-1.172zM12 18q2.484 0 4.242-1.758t1.758-4.242-1.758-4.242-4.242-1.758-4.242 1.758-1.758 4.242 1.758 4.242 4.242 1.758zM20.016 8.672l3.281 3.328-3.281 3.328v4.688h-4.688l-3.328 3.281-3.328-3.281h-4.688v-4.688l-3.281-3.328 3.281-3.328v-4.688h4.688l3.328-3.281 3.328 3.281h4.688v4.688z"></path>
</symbol>
<symbol id="apkm-theme-dark" viewBox="0 0 24 24">
<path d="M12.328 2.016q-2.156-0.094-4.031 0.68t-3.281 2.156-2.203 3.211-0.797 3.938q0 2.063 0.773 3.867t2.156 3.188 3.188 2.156 3.867 0.773q1.875 0 3.516-0.633t2.953-1.758 2.203-2.625q-2.25-0.047-4.102-0.984t-3.164-2.484-1.945-3.469-0.445-3.984 1.313-4.031z"></path>
</symbol>
<symbol id="apkm-theme-auto" viewBox="0 0 24 24">
<path d="M14.297 15.984h1.922l-3.234-9h-1.969l-3.234 9h1.922l0.703-1.969h3.188zM20.016 8.672l3.281 3.328-3.281 3.328v4.688h-4.688l-3.328 3.281-3.328-3.281h-4.688v-4.688l-3.281-3.328 3.281-3.328v-4.688h4.688l3.328-3.281 3.328 3.281h4.688v4.688zM10.828 12.656l1.172-3.656 1.172 3.656h-2.344z"></path>
</symbol>
<symbol id="apkm-icon-user" viewBox="0 0 24 24">
<path d="M12 12.984q1.5 0 3.281 0.422t3.258 1.406 1.477 2.203v3h-16.031v-3q0-1.219 1.477-2.203t3.258-1.406 3.281-0.422zM12 3.984q1.641 0 2.813 1.195t1.172 2.836-1.172 2.813-2.813 1.172-2.813-1.172-1.172-2.813 1.172-2.836 2.813-1.195zM12 14.906q-2.063 0-4.078 0.773t-2.016 1.336v1.078h12.188v-1.078q0-0.563-2.016-1.336t-4.078-0.773zM12 5.906q-0.891 0-1.5 0.609t-0.609 1.5 0.609 1.477 1.5 0.586 1.5-0.586 0.609-1.477-0.609-1.5-1.5-0.609z"></path>
</symbol>
<symbol id="apkm-bluestacks" viewBox="0 0 24 24">
<path fill="#1177b9" style="fill: var(--color3, #1177b9)" d="M3.436 16.447l5.973 2.791c0.693 0.315 1.413 0.569 2.181 0.466 0.459-0.062 0.885-0.24 1.378-0.494 1.906-0.987 6.892-4.684 6.892-4.684 0.727-0.624 0.473-1.797-0.446-2.071-0.007 0-0.007 0-0.014-0.007-2.345-0.699-4.697-1.351-7.049-2.023-0.535-0.151-1.131-0.178-1.543-0.11-0.336 0.055-0.933 0.233-1.296 0.494 0 0-5.458 3.669-5.788 3.929-0.261 0.206-0.569 0.377-0.665 0.747-0.048 0.192-0.048 0.37 0.034 0.569 0.048 0.13 0.137 0.267 0.343 0.391z"></path>
<path fill="#df001f" style="fill: var(--color4, #df001f)" d="M3.436 14.431l5.973 2.791c0.693 0.315 1.413 0.569 2.181 0.466 0.459-0.062 0.885-0.24 1.378-0.494 1.906-0.987 6.892-4.684 6.892-4.684 0.727-0.624 0.473-1.797-0.446-2.071-0.007 0-0.007 0-0.014-0.007-2.345-0.699-4.697-1.351-7.049-2.023-0.535-0.151-1.131-0.178-1.543-0.11-0.336 0.055-0.933 0.233-1.296 0.494 0 0-5.458 3.669-5.788 3.929-0.261 0.206-0.569 0.377-0.665 0.747-0.048 0.192-0.048 0.37 0.034 0.569 0.048 0.123 0.137 0.267 0.343 0.391z"></path>
<path fill="#f4d000" style="fill: var(--color5, #f4d000)" d="M3.436 12.429l5.973 2.791c0.693 0.315 1.413 0.569 2.181 0.466 0.459-0.062 0.885-0.24 1.378-0.494 1.906-0.987 6.892-4.684 6.892-4.684 0.727-0.624 0.473-1.797-0.446-2.071-0.007 0-0.007 0-0.014-0.007-2.345-0.699-4.697-1.351-7.049-2.023-0.535-0.151-1.131-0.178-1.543-0.11-0.336 0.055-0.933 0.233-1.296 0.494 0 0-5.458 3.669-5.788 3.929-0.261 0.206-0.569 0.377-0.665 0.747-0.048 0.192-0.048 0.37 0.034 0.569 0.048 0.123 0.137 0.267 0.343 0.391z"></path>
<path fill="#6db346" style="fill: var(--color6, #6db346)" d="M3.436 10.427l5.973 2.791c0.693 0.315 1.413 0.569 2.181 0.466 0.459-0.062 0.885-0.24 1.378-0.494 1.906-0.987 6.892-4.684 6.892-4.684 0.727-0.624 0.473-1.797-0.446-2.071-0.007 0-0.007 0-0.014-0.007-2.345-0.693-4.697-1.344-7.049-2.023-0.535-0.151-1.131-0.178-1.543-0.11-0.336 0.055-0.933 0.233-1.296 0.494 0 0-5.458 3.669-5.788 3.929-0.261 0.206-0.569 0.377-0.665 0.747-0.048 0.192-0.048 0.37 0.034 0.569 0.048 0.13 0.137 0.267 0.343 0.391z"></path>
</symbol>
<symbol id="apkm-icon-article" viewBox="0 0 24 24">
<path d="M23.975 18.675v-13.425c0-0.35-0.4-0.85-0.9-0.85-0.4 0-0.75 0.475-0.75 0.85v13.425c0 0 0 0.475-0.25 0.675-0.075 0.3-0.225 0.3-0.625 0.3-0.125 0-0.525 0-0.625-0.3-0.125 0-0.125-0.075-0.125-0.2v-16.45h-13.7l-7 7c0 0 0 8.75 0 10.35 0 1.625 1.325 1.225 1.325 1.225h19.4c0 0 0.625 0 0.75 0 1 0 1.625-0.35 1.75-0.675 0.8-0.7 0.75-1.625 0.75-1.925v0zM9.2 11.825h-6.4c-0.525 0-0.75-0.475-0.75-0.95 0-0.525 0.225-0.8 0.75-0.8h4.775v-4.75c0-0.425 0.325-0.75 0.75-0.75 0.475 0 0.875 0.325 0.875 0.75v6.5zM16.65 19.175h-7.2v-1.675h7.2v1.675zM18.575 16.35h-9.125v-1.625h9.125v1.625z"></path>
</symbol>
<symbol id="apkm-icon-auto" viewBox="0 0 24 24">
<path d="M18.92 6.010c-0.2-0.59-0.76-1.010-1.42-1.010h-11c-0.66 0-1.21 0.42-1.42 1.010l-2.080 5.99v8c0 0.55 0.45 1 1 1h1c0.55 0 1-0.45 1-1v-1h12v1c0 0.55 0.45 1 1 1h1c0.55 0 1-0.45 1-1v-8l-2.080-5.99zM6.5 16c-0.83 0-1.5-0.67-1.5-1.5s0.67-1.5 1.5-1.5 1.5 0.67 1.5 1.5-0.67 1.5-1.5 1.5zM17.5 16c-0.83 0-1.5-0.67-1.5-1.5s0.67-1.5 1.5-1.5 1.5 0.67 1.5 1.5-0.67 1.5-1.5 1.5zM5 11l1.5-4.5h11l1.5 4.5h-14z"></path>
</symbol>
<symbol id="apkm-icon-calendar" viewBox="0 0 24 24">
<path d="M20.025 2.725h-1.025v-2.725h-2.575v2.725h-8.925v-2.725h-2.625v2.725h-0.875c-1.375 0-2.525 1.15-2.525 2.525v16.2c0 1.45 1.15 2.55 2.525 2.55h16.025c1.375 0 2.5-1.1 2.5-2.525v-16.2c0-1.375-1.125-2.55-2.5-2.55v0zM19.75 20.675h-15.85v-12.15h15.85v12.15zM17.625 18.625h-5.275v-5.175h5.275v5.175z"></path>
</symbol>
<symbol id="apkm-icon-chevron" viewBox="0 0 24 24">
<path d="M8.625 21l-2.1-2.125 6.875-6.85-6.95-6.95 2.125-2.075 9 9.025-8.95 8.975z"></path>
</symbol>
<symbol id="apkm-icon-devices" viewBox="0 0 24 24">
<path d="M4.025 6h17.975v-2h-17.975c-1.125 0-2 0.925-2 2v11h-2.025v3h14v-3h-9.975v-11zM23 8h-6c-0.55 0-0.975 0.45-0.975 1.025v10c0 0.55 0.425 0.975 0.975 0.975h6c0.575 0 1-0.425 1-0.975v-10c0-0.575-0.425-1.025-1-1.025v0zM22 17.025h-3.975v-7.025h3.975v7.025z"></path>
</symbol>
<symbol id="apkm-icon-download" viewBox="0 0 24 24">
<path d="M11.975 18.075l9.9-9.625h-5.55v-8.45h-8.5v8.45h-5.7l9.85 9.625zM2.125 24h19.725v-2.9h-19.725v2.9z"></path>
</symbol>
<symbol id="apkm-icon-dpi" viewBox="0 0 24 24">
<path d="M17.425 0h-10.85c-1.2 0-2.175 0.975-2.175 2.175v19.625c0 1.2 0.975 2.175 2.175 2.175h10.875c1.225 0 2.175-0.975 2.175-2.175v-19.625c0-1.2-0.95-2.175-2.2-2.175v0zM6.575 19.6v-15.275h10.875v2.275h-3.25v1.1h3.25v2.15h-8.7v1.1h8.7v2.1h-3.25v1.1h3.25v2.075h-3.25v1.1h3.25v2.275h-10.875z"></path>
</symbol>
<symbol id="apkm-icon-exclamation" viewBox="0 0 24 24">
<path d="M12 0c-6.625 0-12 5.375-12 12s5.375 12 12 12c6.6 0 12-5.375 12-12s-5.4-12-12-12v0zM13.6 4.475l-0.35 10.35h-2.475l-0.35-10.35h3.175zM13.525 18.6c-0.075 0.175-0.2 0.325-0.35 0.5-0.15 0.15-0.325 0.275-0.525 0.35-0.175 0.075-0.425 0.075-0.625 0.075-0.275 0-0.525 0-0.7-0.075-0.2-0.075-0.4-0.2-0.525-0.35l-0.35-0.5c-0.075-0.15-0.075-0.4-0.075-0.6s0-0.45 0.075-0.625c0.15-0.15 0.2-0.4 0.35-0.525s0.325-0.2 0.525-0.275c0.175-0.075 0.425-0.15 0.7-0.15 0.2 0 0.45 0.075 0.625 0.15 0.2 0.075 0.4 0.15 0.525 0.275 0.15 0.15 0.275 0.4 0.35 0.525 0.075 0.175 0.175 0.425 0.175 0.625s-0.1 0.45-0.175 0.6z"></path>
</symbol>
<symbol id="apkm-icon-facebook" viewBox="0 0 24 24">
<path d="M13.925 12.175h3.425l0.375-4.175h-3.8v-2.125c0-1.025 0.075-1.7 1.675-1.7h2.15v-4.2h-3.45c-3.95 0-4.925 2.15-4.925 5.475v2.55h-3.1v4.175h3.1v11.8h4.55v-11.8z"></path>
</symbol>
<symbol id="apkm-icon-file" viewBox="0 0 24 24">
<path d="M21.1 0h-18.25c-1.575 0-2.85 1.275-2.85 2.9v18.25c0 1.55 1.275 2.85 2.85 2.85h18.25c1.55 0 2.9-1.3 2.9-2.85v-18.25c0-1.625-1.35-2.9-2.9-2.9v0zM14.825 18.675h-9.5v-2.475h9.5v2.475zM18.625 13.35h-13.3v-2.475h13.3v2.475zM18.625 8h-13.3v-2.5h13.3v2.5z"></path>
</symbol>
<symbol id="apkm-icon-filesize" viewBox="0 0 24 24">
<path d="M22.225 0h-20.475c-0.975 0-1.75 0.775-1.75 1.75v7.075c0 0.975 0.775 1.8 1.75 1.8h20.475c0.975 0 1.75-0.8 1.75-1.8v-7.075c0-0.975-0.775-1.75-1.75-1.75v0zM5.125 8.075c-1.55 0-2.75-1.275-2.75-2.725 0-1.575 1.2-2.85 2.75-2.85 1.475 0 2.725 1.275 2.725 2.85 0 1.45-1.25 2.725-2.725 2.725v0zM22.225 13.425h-20.475c-0.975 0-1.75 0.75-1.75 1.725v7.1c0 0.975 0.775 1.75 1.75 1.75h20.475c0.975 0 1.75-0.775 1.75-1.75v-7.1c0-0.975-0.775-1.725-1.75-1.725v0zM5.125 21.5c-1.55 0-2.75-1.275-2.75-2.775 0-1.525 1.2-2.775 2.75-2.775 1.475 0 2.725 1.275 2.725 2.775s-1.25 2.775-2.725 2.775z"></path>
</symbol>
<symbol id="apkm-icon-hamburger" viewBox="0 0 24 24">
<path d="M3 18h18v-2h-18v2zM3 13h18v-2h-18v2zM3 6v2h18v-2h-18z"></path>
</symbol>
<symbol id="apkm-icon-info" viewBox="0 0 24 24">
<path d="M11.675-0.050c-5.775 0-11.7 5.775-11.7 12.325 0 7.075 5.925 11.7 11.7 11.7 7.125 0 12.3-4.625 12.3-11.7 0-6.55-5.175-12.325-12.3-12.325v0zM13.025 18.025h-2.65v-7.1h2.65v7.1zM13.025 8.325h-2.65v-2.6h2.65v2.6z"></path>
</symbol>
<symbol id="apkm-icon-linkedin" viewBox="0 0 24 24">
<path d="M19.5 19.473v-5.54c0-2.72-0.587-4.813-3.766-4.813-1.527 0-2.552 0.837-2.97 1.63h-0.043v-1.38h-3.009v10.101h3.137v-4.995c0-1.32 0.247-2.595 1.882-2.595 1.611 0 1.631 1.508 1.631 2.678v4.913h3.139zM6.173 7.991v0c1.005 0 1.821-0.815 1.821-1.821s-0.815-1.821-1.821-1.821v0c-1.005 0-1.821 0.815-1.821 1.821s0.815 1.821 1.821 1.821zM4.599 9.371v10.101h3.142v-10.101h-3.143z"></path>
</symbol>
<symbol id="apkm-icon-phone" viewBox="0 0 24 24">
<path d="M16 1h-8c-1.66 0-3 1.34-3 3v16c0 1.66 1.34 3 3 3h8c1.66 0 3-1.34 3-3v-16c0-1.66-1.34-3-3-3zM14 21h-4v-1h4v1zM17.25 18h-10.5v-14h10.5v14z"></path>
</symbol>
<symbol id="apkm-icon-playdownload" viewBox="0 0 24 24">
<path d="M0.496 20.327l7.42-8.285-7.488-8.368c-0.262 0.103-0.428 0.387-0.428 0.809v15.016c0 0.455 0.196 0.749 0.496 0.829zM1.527 20.008l6.818-7.487 2.195 2.453-9.013 5.034zM10.602 9.042l-2.244 2.506-6.643-7.471 8.888 4.965zM11.205 9.379l-2.417 2.654 2.328 2.619 3.57-1.994c0.665-0.372 0.659-0.965 0-1.333l-3.482-1.945z"></path>
<path d="M16.653 13.249l3.208 4.629 3.208-4.629z"></path>
<path d="M19.102 5.633h1.469v12.245h-1.469v-12.245z"></path>
</symbol>
<symbol id="apkm-icon-random" viewBox="0 0 24 24">
<path d="M21.67 3.955l-2.825-2.202.665-.753 4.478 3.497-4.474 3.503-.665-.753 2.942-2.292h-4.162c-3.547.043-5.202 3.405-6.913 7.023 1.711 3.617 3.366 6.979 6.913 7.022h4.099l-2.883-2.247.665-.753 4.478 3.497-4.474 3.503-.665-.753 2.884-2.247h-4.11c-3.896-.048-5.784-3.369-7.461-6.858-1.687 3.51-3.592 6.842-7.539 6.858h-2.623v-1h2.621c3.6-.014 5.268-3.387 6.988-7.022-1.72-3.636-3.388-7.009-6.988-7.023h-2.621v-1h2.623c3.947.016 5.852 3.348 7.539 6.858 1.677-3.489 3.565-6.81 7.461-6.858h4.047z"></path>
</symbol>
<symbol id="apkm-icon-playstore" viewBox="0 0 24 24">
<path d="M16.8 5.4v-2.8c0-1.225-0.95-2.175-2.075-2.175h-5.6c-1.125 0-2.1 0.95-2.1 2.175v2.8h-7.025v15.625c0 1.4 1.2 2.55 2.55 2.55h18.875c1.4 0 2.55-1.15 2.55-2.55v-15.625h-7.175zM9.55 2.8h4.95v2.6h-4.95v-2.6zM8.35 19.725v-10.6l9.2 4.9-9.2 5.7z"></path>
</symbol>
<symbol id="apkm-icon-pushbullet" viewBox="0 0 24 24">
<path d="M12 0c-6.625 0-12 5.375-12 12s5.375 12 12 12 12-5.375 12-12-5.375-12-12-12zM8.65 17.45c0.025 0.425-0.35 0.8-0.8 0.8h-2.125c-0.425 0-0.8-0.375-0.8-0.8v-10.875c0-0.475 0.375-0.8 0.8-0.8h2.125c0.45 0 0.8 0.35 0.8 0.8v10.875zM14.575 18.25h-3.6c-0.45 0-0.85-0.375-0.85-0.8v-10.875c0-0.45 0.375-0.8 0.85-0.8h3.6c3.075 0 5.525 2.775 5.525 6.225s-2.45 6.25-5.525 6.25z"></path>
</symbol>
<symbol id="apkm-icon-qrcode" viewBox="0 0 24 24">
<path d="M2.3 8.025h5.575c0.15 0 0.15 0.025 0.15-0.025 0.025-0.175 0-0.325 0-0.5v-7.3c0-0.025 0.025-0.1 0-0.175 0 0-0.2 0-0.275 0h-7.575c-0.15 0-0.15-0.050-0.175 0.050v7.825c0 0.025 0 0.1 0 0.1 0.025 0.025 0.25 0 0.275 0h2.025zM1.15 5.725v-4.225c0-0.075-0.025-0.2 0-0.25 0-0.1 0-0.1 0.025-0.1h5.525c0.025 0 0.175 0 0.175 0 0.025 0.1 0 0.2 0 0.275v5.175c0 0.025 0.025 0.175 0 0.25 0 0.025 0 0.025-0.075 0.025h-5.5c-0.025 0-0.15 0-0.15 0-0.025-0.025 0-0.175 0-0.2v-0.95zM2.3 14.9v-3.3c0-0.025 0-0.15-0.025-0.15 0-0.075-0.075 0-0.175 0h-0.95c-0.025-0.075 0 0 0-0.075-0.025-0.025 0-0.1 0-0.15v-1.975c0-0.15 0-0.175-0.075-0.175h-1c-0.075 0-0.075 0-0.075 0.175v5.625c0.025 0.025 0.175 0 0.25 0h2.050zM4.625 5.725h1.025c0.1 0 0.075-0.025 0.075-0.025v-3.275c0-0.075 0.025-0.075-0.075-0.1h-3.15c-0.025 0-0.2 0-0.2 0.025-0.025 0 0 0.2 0 0.275v2.825c0 0.075-0.025 0.25 0 0.275 0 0 0.15 0 0.175 0h2.15zM4.625 11.475v1.1c0 0.025 0 0.025 0.025 0.025h0.925c0.025 0 0.15 0 0.15-0.025 0 0 0-0.025 0-0.1v-3.025c0-0.075 0.025-0.325 0-0.35 0 0-0.15 0-0.175 0h-0.875c-0.025 0-0.025 0-0.025 0.15v1.025c-0.075 0.075-0.325 0-0.35 0h-1.875c-0.025 0-0.15 0-0.15 0.075-0.025 0.025 0 0.075 0 0.1v0.7c0 0.025-0.025 0.25 0 0.25 0 0.075 0.15 0.075 0.175 0.075h2.175zM3.425 13.725v1.15h2.1c0.025 0 0.1 0.025 0.175 0 0 0 0 0 0-0.025 0.025-0.2 0-0.4 0-0.6v-0.275c0-0.075 0.025-0.175 0-0.2 0-0.025 0.025-0.025-0.075-0.025 0-0.025-0.075 0-0.15 0h-0.625c-0.1 0-0.25 0.025-0.25-0.025 0 0 0-0.1 0-0.15v-0.95c-0.075-0.025-0.175 0-0.2 0h-0.975v1.1zM3.425 18.275h-1.075c-0.1 0.025-0.075 0.025-0.075 0.1v3.2c0 0.075-0.025 0.075 0.075 0.15 0.075 0.025 0.25 0 0.35 0h2.825c0.025 0 0.175 0 0.175 0 0.025-0.075 0-0.25 0-0.325v-2.8c0-0.1 0.025-0.25 0-0.325 0-0.025-0.1-0.025-0.15-0.025h-2.125zM5.7 15.975h-5.525c-0.15 0-0.15 0-0.175 0.075v7.95c0.025 0.025 0.25 0 0.275 0h7.575c0.15 0 0.15 0.025 0.15-0.025 0.025-0.35 0-0.675 0-1.025v-6.85c0 0 0.025-0.1 0-0.15 0 0-0.2 0-0.275 0h-2.025zM6.875 18.275v4.275c0 0.075 0.025 0.2 0 0.275 0 0.025 0 0.025-0.075 0.025h-5.5c-0.025 0-0.15 0-0.15 0-0.025-0.025 0-0.175 0-0.2v-5.15c0-0.1-0.025-0.25 0-0.325 0-0.025 0-0.025 0.025-0.025h5.5c0.025 0 0.175 0 0.175 0 0.025 0.025 0 0.25 0 0.25v0.875zM8 13.725h-0.7c-0.15 0-0.275-0.025-0.4 0-0.025 0-0.025 0-0.025 0.075v0.875c0 0.025 0 0.2 0.025 0.2 0.025 0.025 0.2 0 0.25 0h1.925c0.025 0 0 0 0.025-0.025v-1.125c-0.025-0.025-0.25 0-0.275 0h-0.825zM10.3 0.025h-1.15c-0.025 0 0 0.1 0 0.1v0.875c0 0.025-0.025 0.1 0 0.15 0 0.1 0.275 0 0.275 0h0.675c0.025 0 0.15 0.1 0.175 0 0.025 0 0.025 0 0.025-0.025v-1.1zM10.3 12.6h0.85c0.025 0 0.25 0.025 0.275 0s0-0.25 0-0.325v-0.825c0.025-0.075 0.2 0 0.25 0h0.875c0.075-0.075 0-0.25 0-0.275v-0.725c0-0.025 0-0.1 0-0.1-0.025-0.075-0.2-0.075-0.25-0.075h-1.875c0 0-0.1 0-0.15 0-0.025 0.075 0 0.175 0 0.2v0.925c-0.025 0.075-0.1 0.075-0.2 0.075h-0.925c-0.025-0.075 0-0.15 0-0.2v-0.775c0-0.025-0.025-0.1 0-0.15 0 0-0.025 0 0-0.075 0 0 0.175 0 0.2 0h0.7c0.075 0 0.175 0.075 0.2 0 0 0 0.025 0.075 0.025 0v-3.3c0-0.1 0-0.075 0.1-0.075h0.95c0.075 0 0.075 0 0.075-0.1v-2.1c0-0.025-0.025-0.025 0.075-0.025h0.95c0.1 0 0.1-0.025 0.1 0.025v0.95c0 0.075-0.025 0.1 0.075 0.1h1.050c0 0 0 0 0 0.025 0.075 0.175 0 0.275 0 0.425v1.625c0 0.1 0 0.175 0.075 0.175 0.025 0.025 0.1 0 0.1 0h0.8c0.025 0 0.1 0.025 0.2 0 0.025 0 0 0.025 0 0 0.025-0.025 0-0.1 0-0.175v-4.175c0 0 0.025-0.2 0-0.2 0-0.025-0.15 0-0.15 0h-1.925c0 0-0.175 0-0.25 0-0.025-0.025 0-0.25 0-0.325v-3.1c-0.025-0.025-0.2 0-0.25 0h-0.9c-0.025 0.075 0 0.2 0 0.25v3.15c-0.025 0.025-0.15 0.025-0.2 0.025h-1.825c-0.075 0-0.2-0.025-0.25 0-0.025 0 0 0.1 0 0.175v5.45c0 0.025-0.025 0-0.075 0h-2.1c-0.075 0-0.075 0-0.075 0.15v0.95c0 0.075 0 0.075 0.075 0.075h0.95c0.1 0 0.1 0 0.1 0.1 0.025 0.325 0 0.625 0 0.95 0 0 0.025 0.075 0 0.075 0 0.075 0 0.075-0.025 0.075h-0.925c-0.025 0-0.1-0.075-0.1 0-0.025 0-0.025 0-0.025 0.025v0.925c0 0.025 0 0.15 0 0.15 0.025 0.025 0 0 0.025 0.025h2.075c0.1 0 0.15-0.025 0.175 0v0.975c0 0.025-0.025 0.15 0 0.175h1.15c0-0.025 0-0.15 0-0.15v-0.975zM9.125 19.4h1.15c0 0 0-0.025 0-0.075v-0.875c0-0.025 0-0.15 0-0.15-0.025-0.025-0.25-0.025-0.325-0.025h-0.85c-0.025 0.025 0 0.025 0 0.1-0.025 0.025 0 0.1 0 0.25v0.775zM9.125 23.975h0.975c0.075 0 0.15 0.025 0.175 0v-0.975c0 0 0-0.1 0-0.15-0.025-0.025-0.25 0-0.325 0h-0.85c-0.025 0 0 0 0 0.025-0.025 0.075 0 0.2 0 0.275v0.825zM11.425 9.1h0.75c0.1 0 0.25 0.025 0.35 0 0.1 0 0.025 0 0.025-0.025v-1.85c0-0.1 0-0.175 0-0.175v-0.175c0 0-0.075 0-0.1 0h-1.050c0 0 0 0 0 0.025v2.2zM14.875 1.15h-1.2c0 0.1 0 0.1 0 0.1v0.9c0 0.025 0 0.15 0 0.15 0.075 0.025 0.275 0 0.35 0h0.6c0.025 0 0.15 0.025 0.25 0 0.025 0 0 0 0-0.025 0.025-0.075 0-0.15 0-0.275v-0.85zM16 23.975h0.95c0.025 0 0.15 0.025 0.175 0v-0.975c0 0 0-0.1 0-0.15-0.025-0.025-0.25 0-0.325 0h-0.8c-0.025 0 0 0 0 0.025-0.025 0.075 0 0.2 0 0.275v0.825zM17.125 22.85h1.15v-1.125c-0.025-0.075-0.275 0-0.325 0h-0.625c-0.025 0-0.175-0.075-0.2 0-0.025 0 0 0 0 0.025-0.025 0.025 0 0.175 0 0.25v0.85zM18.3 15.975h-2c-0.075 0-0.275 0-0.275 0.025-0.025 0.025 0 0.15 0 0.2v0.875c-0.025 0.025 0 0-0.025 0-0.075 0.025-0.2 0-0.25 0h-0.7c-0.025 0-0.1 0-0.1 0-0.025 0.025-0.025 0.025-0.025 0.1v0.95c0 0 0.025 0.1 0 0.1 0 0.025-0.15 0-0.15 0h-0.925c0 0-0.075 0-0.15 0.025v3.175c0 0.075 0.075 0.15 0 0.175 0 0.075 0 0.075-0.025 0.075h-0.925c0 0-0.1 0-0.175 0-0.025-0.075 0-0.35 0-0.4v-1.775c0-0.025 0-0.1 0-0.1h-1.1c-0.025 0-0.025 0-0.025 0-0.025 0.075 0 0.2 0 0.25v0.875c0 0.075 0 0-0.025 0.075h-0.925c-0.025 0-0.15-0.075-0.175 0-0.025 0 0 0.2 0 0.275v1.85c0 0.025 0 0.15 0 0.15h1.025c0.025 0 0.075 0 0.075 0v1.075c0 0.025 0 0.025 0.025 0.025 0.025 0.025 0.15 0 0.2 0h1.875c0.025 0 0.15 0.025 0.15 0 0 0 0-0.025 0-0.1v-1.025c0.075 0 0.15 0 0.15 0h1.050c0.025 0 0-0.15 0-0.175v-0.625c0-0.075 0-0.275 0.025-0.325 0-0.075 0.1 0 0.1 0h0.975c0.025 0 0 0 0.025-0.075v-0.875c0-0.025-0.025-0.15 0-0.175 0 0 0 0 0.025 0h0.925c0 0 0.15 0 0.175 0 0 0 0-0.1 0-0.175v-2.65c0-0.2-0.025-0.425 0-0.575 0-0.1-0.025-0.075 0-0.1h2.075c0.025 0 0.15 0.025 0.175 0 0.025 0 0-0.25 0-0.275v-0.85c0 0-0.025 0-0.075 0h-1zM19.375 23.975h0.75c0.15 0 0.25 0.025 0.4 0 0.075 0 0.025 0 0.025-0.1v-1.025c-0.025-0.025-0.25 0-0.275 0h-1.875c-0.075 0-0.1 0-0.1 0s0 0 0 0.025v0.875c0 0.025-0.025 0.25 0 0.25 0.025 0.025 0.2 0 0.25 0h0.825zM19.375 2.3h-1.050c-0.075 0.025-0.025 0.025-0.025 0.1v3.2c0 0.075-0.025 0.075 0.025 0.1 0.075 0.025 0.2 0 0.35 0h2.8c0.075 0 0.2 0 0.2 0 0.025-0.025 0-0.2 0-0.25v-2.825c0 0 0.025-0.275 0-0.275 0-0.025-0.1-0.025-0.15-0.025h-2.15zM19.375 9.1h-5.625c-0.075 0.025-0.075 0-0.075 0.15v3.225c0 0.075 0.075 0.1 0 0.15h-0.975c0 0-0.15-0.025-0.15 0-0.025 0.025 0 0.15 0 0.2v0.775c0 0 0 0.1 0 0.15h-0.925c-0.025 0-0.2-0.025-0.2 0 0 0 0 0.1 0 0.175v1.85c0 0.075 0.025 0.25 0 0.25 0 0-0.075 0-0.1 0h-0.775c-0.075 0-0.15 0.025-0.25 0-0.025 0 0 0 0-0.025v-1.025c-0.025-0.025 0-0.025-0.075-0.025h-1.050c-0.075 0-0.025 0-0.025 0.025-0.025 0.025 0 0.175 0 0.25v1.475c0 0.1-0.025 0.45 0 0.45 0.025 0.025 0.25 0 0.275 0h0.8c0 0 0.075 0 0.1 0.025v1.025c0 0-0.025 0.1 0 0.1 0 0.025 0.15 0 0.175 0h0.825c0 0 0.1 0.025 0.15 0v-0.95c0 0-0.025-0.2 0-0.25h1.925c0.15 0 0.25 0.025 0.275 0 0.075 0 0-0.275 0-0.4v-0.7c0.075-0.025 0-0.025 0.1-0.025h0.725c0.025 0 0.175 0.025 0.25 0 0.15 0 0.1 0.025 0.1 0 0.025-0.1 0-0.25 0-0.325v-0.7c0-0.075 0.025-0.075 0-0.075-0.15-0.025-0.25 0-0.325 0h-0.9v-1.050c0-0.025 0-0.1 0-0.1 0.075-0.025 0.175 0 0.275 0h0.925c0.025-0.025 0-0.15 0-0.175v-0.8c0-0.025 0-0.15 0-0.15 0.025-0.025 0.1 0 0.15 0h0.95c0.025-0.025 0.025-0.15 0.025-0.175v-0.75c0 0-0.025-0.2 0-0.2 0-0.075 0.075 0 0.075 0h1c0.025-0.075 0.025-0.1 0.025-0.15v-0.7c0-0.15-0.025-0.2 0-0.275 0-0.075 0-0.075 0.025-0.075h2.95c0.1 0 0.4 0.075 0.425 0 0.025-0.025 0-0.2 0-0.25v-0.925h-1.075zM20.55 17.1h-1.15c-0.075 0.025-0.025 0.025-0.025 0.175v2c0 0.025 0 0.1 0 0.1s0.075 0 0.15 0h1.050v-2.275zM21.675 14.9h-0.75c-0.075 0-0.2 0.025-0.35 0-0.025 0-0.025 0-0.025-0.1v-2.075c0 0 0-0.075 0-0.1s-0.1 0-0.15 0h-0.9c-0.075 0-0.15-0.025-0.15 0 0 0 0 0 0 0.025v0.875c0 0 0.025 0.175 0 0.2-0.025 0-0.175 0-0.175 0h-0.875c-0.025 0-0.025 0-0.025-0.1v-0.825c0-0.075 0-0.2 0-0.2-0.025-0.025-0.275 0-0.325 0h-0.65c-0.025 0-0.15-0.025-0.175 0-0.025 0 0 0.025 0 0.1v0.875c0 0.025 0 0.1-0.025 0.15 0 0-0.1 0-0.15 0h-0.675c-0.025 0-0.25-0.025-0.275 0-0.025 0 0 0.1 0 0.175v0.9c0 0.1-0.025 0.1 0.025 0.1 0.275 0.025 0.575 0 0.875 0h2.35c0.075 0 0.15 0 0.15 0 0 0.025 0 0.075 0 0.075v1c0 0.025 0.15 0 0.2 0h1.8c0.075 0 0.275 0 0.275 0 0.025 0.025 0 0.2 0 0.25v0.85c0 0.075 0 0.025 0.075 0.025h0.925c0.025 0 0.1 0.025 0.1 0 0.1-0.025 0-0.2 0-0.25v-0.85c0.1-0.025 0.15-0.025 0.175-0.025h0.875c0.075 0 0.1 0.025 0.175 0 0 0 0-0.1 0-0.15v-0.95c-0.075 0-0.2 0-0.2 0h-2.125zM22.775 0.025h-6.7c-0.075 0-0.075 0-0.075 0.1v7.775c0 0.025-0.025 0.1 0 0.1 0 0.025 0.2 0 0.25 0h7.6c0.15 0 0.15 0.025 0.15-0.075v-7.9c-0.075-0.025-0.325 0-0.35 0h-0.875zM22.775 2.3v4.3c0 0.025 0.1 0.175 0 0.25 0 0.025 0 0.025 0 0.025h-5.5c-0.025 0-0.15 0-0.15 0-0.025-0.025 0-0.175 0-0.2v-5.225c0-0.025-0.025-0.2 0-0.2 0-0.1 0-0.1 0.025-0.1h5.575c0.025 0 0.075 0 0.075 0 0.1 0.1 0 0.2 0 0.275v0.875zM22.775 10.275h-2.050c-0.025 0-0.15 0-0.175 0.075v0.95c0 0.025 0 0.1 0 0.1 0.025 0.075 0.1 0.075 0.175 0.075h0.875c0.025 0 0.075-0.075 0.075 0 0.025 0.1 0 0.275 0 0.35v0.775c0.025 0 0.175 0 0.175 0h0.925c0.1 0 0 0 0-0.075v-1.075c0.1-0.075 0.275 0 0.35 0h0.8c0.075-0.075 0.075-0.1 0.075-0.15v-1.875c0 0 0-0.325 0-0.35h-1.075c-0.15 0-0.025 0-0.15 0.15v1.050zM24 13.725v-1.125h-1c-0.025 0-0.15-0.025-0.15 0-0.1 0-0.1 0.25-0.1 0.275v0.675c0 0.025 0 0.15 0 0.15 0.1 0.025 0 0.025 0.1 0.025h1.15zM22.775 19.4h-2.050c0 0-0.15 0-0.175 0 0 0 0 0.1 0 0.175v0.625c0 0.075 0.025 0.325 0 0.4h-2.25c-0.025 0.025 0 0.15 0 0.175v0.7c0 0.025-0.025 0.175 0 0.25h3.35c0.025 0 0-0.15 0-0.175v-0.9c0.025-0.1 0-0.025 0.025-0.025 0.075-0.075 0.15 0 0.275 0h1.925c0.025 0 0.025 0 0.1-0.075v-3.175c0 0 0-0.15 0-0.2-0.075-0.025-0.35-0.025-0.4-0.025h-0.85v2.25zM22.775 23.975h1.050c0.025 0 0.1 0.025 0.175 0v-0.975c0 0 0-0.1 0-0.15-0.075-0.025-0.325 0-0.35 0h-0.775c-0.1 0 0 0-0.1 0.025v1.1z"></path>
</symbol>
<symbol id="apkm-icon-reddit" viewBox="0 0 24 24">
<path d="M20.25 11.625c0 0.006 0 0.012 0 0.019 0 0.711-0.403 1.327-0.993 1.633l-0.010 0.005c0.074 0.285 0.11 0.583 0.11 0.893 0 0.962-0.328 1.852-0.981 2.672s-1.545 1.466-2.675 1.941c-1.13 0.476-2.357 0.712-3.683 0.712s-2.552-0.237-3.679-0.712c-1.127-0.474-2.017-1.121-2.67-1.94-0.654-0.819-0.981-1.71-0.981-2.672 0-0.291 0.034-0.583 0.101-0.875-0.314-0.157-0.57-0.39-0.75-0.676l-0.005-0.008c-0.19-0.3-0.285-0.632-0.285-0.992 0-0.508 0.178-0.945 0.534-1.308 0.32-0.336 0.77-0.545 1.269-0.545 0.010 0 0.020 0 0.030 0l-0.002-0c0.523 0 0.968 0.195 1.335 0.586 1.339-0.943 2.92-1.446 4.743-1.507l1.068-4.85c0.020-0.082 0.069-0.151 0.137-0.194l0.001-0.001c0.048-0.034 0.108-0.055 0.173-0.055 0.024 0 0.047 0.003 0.069 0.008l-0.002-0 3.398 0.755c0.11-0.23 0.276-0.415 0.496-0.555 0.203-0.131 0.451-0.209 0.716-0.209 0.004 0 0.008 0 0.012 0h-0.001c0.381 0 0.705 0.135 0.977 0.405 0.27 0.27 0.405 0.597 0.405 0.982 0 0.384-0.135 0.713-0.405 0.986s-0.596 0.409-0.977 0.409c-0.38 0-0.705-0.135-0.971-0.405-0.247-0.244-0.4-0.583-0.4-0.957 0-0.008 0-0.017 0-0.025l-0 0.001-3.075-0.69-0.958 4.395c1.841 0.056 3.435 0.552 4.778 1.489 0.319-0.35 0.776-0.568 1.284-0.568 0.012 0 0.023 0 0.035 0l-0.002-0c0.51 0 0.942 0.181 1.297 0.545 0.357 0.363 0.535 0.8 0.535 1.308zM7.599 13.477c0 0.385 0.133 0.712 0.401 0.986s0.59 0.41 0.971 0.41c0.381 0 0.707-0.136 0.976-0.41s0.405-0.602 0.405-0.986-0.135-0.712-0.405-0.982c-0.27-0.27-0.595-0.405-0.975-0.405-0.375 0-0.697 0.137-0.967 0.41-0.25 0.244-0.405 0.584-0.405 0.961 0 0.006 0 0.011 0 0.017v-0.001zM15.057 16.782c0.068-0.067 0.101-0.15 0.101-0.242 0-0.001 0-0.001 0-0.002 0-0.183-0.147-0.331-0.33-0.333h-0c-0.093 0-0.173 0.032-0.24 0.093-0.252 0.261-0.622 0.454-1.114 0.578-0.442 0.117-0.949 0.185-1.473 0.186h-0c-0.524-0.001-1.031-0.069-1.515-0.195l0.042 0.009c-0.491-0.124-0.862-0.317-1.114-0.578-0.061-0.058-0.144-0.093-0.234-0.093-0.002 0-0.004 0-0.006 0h0c-0.002-0-0.005-0-0.008-0-0.087 0-0.165 0.036-0.222 0.093l-0 0c-0.062 0.057-0.101 0.138-0.101 0.228 0 0.003 0 0.007 0 0.010l-0-0c-0 0.002-0 0.005-0 0.007 0 0.094 0.038 0.179 0.101 0.24l0 0c0.264 0.266 0.628 0.477 1.091 0.632s0.838 0.247 1.127 0.274c0.25 0.026 0.542 0.041 0.837 0.042h0.001c0.27 0 0.55-0.015 0.838-0.042s0.664-0.12 1.128-0.274c0.465-0.155 0.828-0.366 1.092-0.633zM15.029 14.874c0.38 0 0.703-0.136 0.971-0.41s0.4-0.602 0.4-0.986c0-0.379-0.135-0.705-0.405-0.977-0.24-0.252-0.578-0.41-0.953-0.41-0.005 0-0.010 0-0.016 0h0.001c-0.38 0-0.705 0.135-0.975 0.405s-0.405 0.598-0.405 0.982 0.135 0.713 0.405 0.986c0.27 0.274 0.595 0.41 0.975 0.41z"></path>
</symbol>
<symbol id="apkm-icon-rss" viewBox="0 0 24 24">
<path d="M3.375 17.35c1.8 0 3.25 1.45 3.25 3.25s-1.45 3.25-3.25 3.25-3.25-1.45-3.25-3.25 1.45-3.25 3.25-3.25zM15.875 23.875h-4.625c0-6.15-4.975-11.125-11.125-11.125v0-4.625c8.7 0 15.75 7.050 15.75 15.75zM19.125 23.875c0-10.5-8.5-19-19-19v-4.75c13.125 0 23.75 10.625 23.75 23.75h-4.75z"></path>
</symbol>
<symbol id="apkm-icon-safe" viewBox="0 0 24 24">
<path d="M12.025 0c-6.625 0-12 5.4-12 11.975 0 6.675 5.375 12.025 12 12.025 6.6 0 11.975-5.35 11.975-12.025 0.025-6.575-5.375-11.975-11.975-11.975v0zM10.775 18.075l-6.45-4.575 1.35-1.975 4.55 3.2 6.025-8.4 1.95 1.375-7.425 10.375z"></path>
</symbol>
<symbol id="apkm-icon-samsungstore" viewBox="0 0 24 24">
<path d="M23.083 6.669l-1.337 12.463c-0.266 2.537-2.4 4.457-4.954 4.457h-9.583c-2.554 0-4.689-1.92-4.954-4.457l-1.337-12.463h4.954l0.094-0.377c0-3.326 2.709-6.034 6.034-6.034s6.043 2.709 6.043 6.034l0.086 0.377h4.954zM15.497 6.669l-0.094-0.377c0-1.877-1.526-3.403-3.403-3.403s-3.403 1.526-3.403 3.403l-0.094 0.377h6.994z"></path>
</symbol>
<symbol id="apkm-icon-sdk" viewBox="0 0 24 24">
<path d="M23.975 10.825v-2.875h-2.65v-2.8c0-1.225-1.025-2.25-2.25-2.25h-3v-2.9h-2.875v2.9h-2.425v-2.9h-2.75v2.9h-2.8c-1.225 0-2.2 1.025-2.2 2.25v2.8h-3v2.875h2.975v2.425h-2.975v2.75h2.975v3.025c0 1.225 0.975 2.125 2.2 2.125h2.8v2.85h2.775v-2.85h2.425v2.85h2.875v-2.85h3.025c1.225 0 2.25-0.9 2.25-2.125v-3.025h2.625v-2.75h-2.65v-2.425h2.65zM18.575 18.6h-13.25v-13.25h13.25v13.25zM15.775 8.125h-7.675v7.675h7.675v-7.675zM13.275 13.325h-2.775v-2.775h2.775v2.775z"></path>
</symbol>
<symbol id="apkm-icon-search" viewBox="0 0 24 24">
<path d="M17.35 15.375h-0.825l-0.575-0.575c1.4-1.5 2.15-3.55 2.15-5.775 0-4.925-4.075-9-8.975-9-4.975-0.025-9.050 4.050-9.050 8.975 0 4.975 4.075 8.975 9.075 8.975 2.125 0 4.075-0.725 5.575-1.975l0.575 0.625v0.775l6.6 6.6 2.025-2.050-6.575-6.575zM9.15 15.175c-3.425 0-6.175-2.775-6.175-6.175 0-3.425 2.75-6.175 6.175-6.175 3.325 0 6.1 2.75 6.1 6.175-0.025 3.4-2.8 6.175-6.1 6.175z"></path>
</symbol>
<symbol id="apkm-icon-searchfilter" viewBox="0 0 24 24">
<path d="M23.378 18.563h-10.42v-0.585c0-0.858-0.698-1.556-1.556-1.556h-2.414c-0.858 0-1.556 0.698-1.556 1.556v0.585h-6.81c-0.344 0-0.622 0.279-0.622 0.622s0.279 0.622 0.622 0.622h6.81v0.585c0 0.858 0.698 1.556 1.556 1.556h2.414c0.858 0 1.556-0.698 1.556-1.556v-0.585h10.42c0.344 0 0.622-0.279 0.622-0.622s-0.279-0.622-0.622-0.622zM11.713 20.393c0 0.172-0.14 0.311-0.311 0.311h-2.414c-0.172 0-0.311-0.14-0.311-0.311v-2.414c0-0.172 0.14-0.311 0.311-0.311h2.414c0.172 0 0.311 0.14 0.311 0.311v2.414z"></path>
<path d="M23.378 11.123h-2.438v-0.585c0-0.858-0.698-1.556-1.556-1.556h-2.414c-0.858 0-1.556 0.698-1.556 1.556v0.585h-14.792c-0.344 0-0.622 0.279-0.622 0.622s0.279 0.622 0.622 0.622h14.792v0.584c0 0.858 0.698 1.556 1.556 1.556h2.414c0.858 0 1.556-0.698 1.556-1.556v-0.584h2.438c0.344 0 0.622-0.279 0.622-0.622s-0.279-0.622-0.622-0.622zM19.695 12.952c0 0.172-0.14 0.311-0.311 0.311h-2.414c-0.172 0-0.311-0.14-0.311-0.311v-2.414c0-0.172 0.14-0.311 0.311-0.311h2.414c0.172 0 0.311 0.14 0.311 0.311v2.414z"></path>
<path d="M23.378 4.192h-14.792v-0.585c0-0.858-0.698-1.556-1.556-1.556h-2.414c-0.858 0-1.556 0.698-1.556 1.556v0.585h-2.438c-0.344 0-0.622 0.279-0.622 0.622s0.279 0.622 0.622 0.622h2.438v0.585c0 0.858 0.698 1.556 1.556 1.556h2.414c0.858 0 1.556-0.698 1.556-1.556v-0.585h14.792c0.344 0 0.622-0.279 0.622-0.622s-0.279-0.622-0.622-0.622zM7.342 6.022c0 0.172-0.14 0.311-0.311 0.311h-2.414c-0.172 0-0.311-0.14-0.311-0.311v-2.414c0-0.172 0.14-0.311 0.311-0.311h2.414c0.172 0 0.311 0.14 0.311 0.311v2.414z"></path>
</symbol>
<symbol id="apkm-icon-share" viewBox="0 0 24 24">
<path d="M19.175 16.65c-0.95 0-1.75 0.35-2.475 0.875l-8.25-4.825c0.025-0.2 0.025-0.5 0.025-0.7s0-0.5-0.025-0.7l8.275-4.9c0.7 0.6 1.525 0.95 2.475 0.95 1.975 0 3.625-1.7 3.625-3.625-0.025-2.025-1.675-3.725-3.65-3.725-2.050 0-3.725 1.7-3.725 3.725 0 0.2 0.075 0.425 0.1 0.675l-8.325 4.825c-0.6-0.525-1.525-0.875-2.35-0.875-2.050 0-3.65 1.625-3.65 3.65s1.625 3.65 3.65 3.65c0.85 0 1.75-0.35 2.35-0.875l8.35 4.825c-0.025 0.25-0.1 0.45-0.1 0.675 0 2.050 1.7 3.725 3.725 3.725 1.975 0 3.625-1.7 3.625-3.725-0.025-1.95-1.675-3.625-3.65-3.625z"></path>
</symbol>
<symbol id="apkm-icon-tag" viewBox="0 0 24 24">
<path d="M17.175 21.2h-14.975c-1.2 0-2.2-1.025-2.2-2.2v-13.75c0-1.25 1-2.45 2.2-2.45h14.975l6.825 9.3-6.825 9.1z"></path>
</symbol>
<symbol id="apkm-icon-telegram" viewBox="0 0 24 24">
<path d="M9.437 14.491l6.090 4.571c0.695 0.39 1.196 0.188 1.37-0.655l2.478-11.869c0.254-1.034-0.388-1.502-1.052-1.196l-14.554 5.702c-0.994 0.405-0.988 0.968-0.18 1.219l3.735 1.185 8.648-5.543c0.407-0.252 0.782-0.117 0.475 0.161z"></path>
</symbol>
<symbol id="apkm-icon-tv" viewBox="0 0 24 24">
<path d="M21 3h-18c-1.1 0-2 0.9-2 2v12c0 1.1 0.9 2 2 2h5v2h8v-2h5c1.1 0 1.99-0.9 1.99-2l0.010-12c0-1.1-0.9-2-2-2zM21 17h-18v-12h18v12z"></path>
</symbol>
<symbol id="apkm-icon-twitter" viewBox="0 0 24 24">
<path d="M24 4.525c-0.85 0.325-1.875 0.675-2.875 0.75 1.075-0.55 1.9-1.625 2.2-2.65-1.050 0.35-1.95 0.925-3.050 1.125-0.9-0.875-2.275-1.6-3.65-1.475-2.75-0.125-4.875 2.175-4.875 4.85 0 0.425 0.1 0.775 0.1 1.125-4.175-0.175-7.675-2.050-10.225-5.25-0.175 0.875-0.625 1.65-0.625 2.6 0 1.725 0.85 3.2 2.175 4.1-0.875 0-1.575-0.325-2.3-0.575 0 2.45 1.725 4.35 4.025 4.95-0.425 0-0.825 0.125-1.275 0.125-0.35 0-0.6-0.050-0.975-0.050 0.7 1.925 2.35 3.35 4.7 3.35-1.75 1.3-3.875 2.050-6.25 2.15-0.3-0.050-0.75-0.050-1.125-0.050 2.2 1.4 4.775 2 7.5 2.15 9.15-0.15 14.075-7.375 14.075-13.975v-0.725c0.95-0.7 1.775-1.525 2.45-2.525z"></path>
</symbol>
<symbol id="apkm-icon-upload" viewBox="0 0 24 24">
<path d="M19.25 10c-0.575-3.475-3.6-6.075-7.2-6.075-2.9 0-5.35 1.675-6.575 4.125-3.1 0.275-5.475 2.85-5.475 5.975 0 3.325 2.725 6.050 6.050 6.050h12.925c2.75 0 5.025-2.25 5.025-5.050 0-2.675-2.1-4.875-4.75-5.025v0zM13.975 13.1v3.95h-3.95v-3.95h-3.050l5-5.025 5 5.025h-3z"></path>
</symbol>
<symbol id="apkm-icon-vr" viewBox="0 0 24 24">
<path d="M19.78 5.33h-15.4c-1.312 0.006-2.374 1.068-2.38 2.379v8.731c0 1.226 0.994 2.22 2.22 2.22v0h4.11c0.816-0.001 1.529-0.442 1.914-1.1l0.006-0.010 1.43-2.48c0.095-0.165 0.27-0.274 0.47-0.274s0.375 0.109 0.469 0.271l0.001 0.003 1.43 2.48c0.39 0.674 1.107 1.12 1.928 1.12 0.008 0 0.015-0 0.023-0h3.999c1.113-0.039 2-0.95 2-2.069 0-0 0-0.001 0-0.001v0-9c0-0.015 0.001-0.032 0.001-0.050 0-1.226-0.994-2.22-2.22-2.22-0 0-0 0-0.001 0h0zM7.69 14.13c-1.226 0-2.22-0.994-2.22-2.22s0.994-2.22 2.22-2.22c1.226 0 2.22 0.994 2.22 2.22v0c0 1.226-0.994 2.22-2.22 2.22v0zM16.62 14.13c-1.226 0-2.22-0.994-2.22-2.22s0.994-2.22 2.22-2.22c1.226 0 2.22 0.994 2.22 2.22v0c0 1.226-0.994 2.22-2.22 2.22v0z"></path>
</symbol>
<symbol id="apkm-icon-vr-daydream" viewBox="0 0 24 24">
<path d="M22.313 16.075c-0.2 1.5-1.5 3.288-3.563 3.825-1.237 0.325-2.712 0.012-3.525-0.462-1.25-0.725-2.262-1.563-3.012-2.475-0.075-0.1-0.225-0.113-0.325-0.025-0.1 0.075-0.113 0.225-0.025 0.325 0.787 0.963 1.85 1.825 3.138 2.587 0.125 0.075 0.263 0.15 0.413 0.213 0.012 0.125 0.025 0.237 0.025 0.363 0 1.175-0.612 2.088-1.725 2.55-1.4 0.575-3.6 0.375-5.112-1.137-0.912-0.912-1.375-2.337-1.375-3.275 0-1.437 0.2-2.712 0.6-3.825 0.038-0.125-0.012-0.25-0.138-0.288s-0.25 0.012-0.288 0.138c-0.413 1.162-0.625 2.5-0.625 3.987 0 0.125 0.012 0.25 0.025 0.375-0.112 0.087-0.225 0.175-0.35 0.237-1.025 0.588-2.112 0.513-3.063-0.225-1.2-0.925-2.112-2.937-1.563-5 0.3-1.263 1.3-2.388 2.125-2.862 1.237-0.713 2.463-1.175 3.625-1.388 0.125-0.025 0.213-0.138 0.188-0.262s-0.138-0.213-0.263-0.188c-1.212 0.213-2.487 0.7-3.775 1.437-0.1 0.063-0.2 0.125-0.3 0.188-0.138-0.050-0.275-0.113-0.4-0.188-1.025-0.588-1.5-1.575-1.338-2.775 0.2-1.5 1.5-3.288 3.563-3.825 1.237-0.325 2.712-0.012 3.525 0.462 1.25 0.725 2.262 1.563 3.012 2.475 0.075 0.1 0.225 0.113 0.325 0.025 0.1-0.075 0.113-0.225 0.025-0.325-0.787-0.963-1.85-1.825-3.138-2.587-0.125-0.075-0.263-0.15-0.413-0.213-0.012-0.125-0.025-0.237-0.025-0.363 0-1.175 0.612-2.088 1.725-2.55 1.4-0.575 3.6-0.375 5.112 1.137 0.912 0.912 1.375 2.337 1.375 3.275 0 1.462-0.213 2.763-0.625 3.888-0.050 0.113 0.012 0.25 0.138 0.3 0.025 0.012 0.050 0.012 0.075 0.012 0.087 0 0.175-0.063 0.213-0.15 0.438-1.175 0.65-2.538 0.65-4.038 0-0.125-0.012-0.25-0.025-0.375 0.112-0.087 0.225-0.175 0.35-0.237 1.025-0.588 2.112-0.513 3.063 0.225 1.2 0.925 2.112 2.937 1.563 5-0.337 1.238-1.338 2.363-2.15 2.825-1.238 0.712-2.463 1.175-3.625 1.388-0.125 0.025-0.213 0.138-0.188 0.263 0.025 0.112 0.113 0.188 0.225 0.188 0.012 0 0.025 0 0.038 0 1.212-0.213 2.487-0.7 3.775-1.437 0.1-0.063 0.2-0.125 0.3-0.188 0.138 0.050 0.275 0.112 0.4 0.188 1.050 0.6 1.525 1.587 1.362 2.787z"></path>
</symbol>
<symbol id="apkm-icon-wearable" viewBox="0 0 24 24">
<path d="M19.975 12.025c0-2.55-1.2-4.775-3.050-6.275l-0.925-5.75h-8l-0.925 5.75c-1.85 1.5-3.050 3.725-3.050 6.275 0 2.525 1.2 4.775 3.050 6.25l0.925 5.725h8l0.925-5.75c1.875-1.45 3.050-3.7 3.050-6.225v0zM5.975 12.025c0-3.325 2.725-6 6.050-6 3.275-0.025 5.975 2.675 5.975 6 0 3.275-2.7 6-5.975 6-3.325 0-6.050-2.725-6.050-6z"></path>
</symbol>
<symbol id="apkm-icon-whatsapp" viewBox="0 0 24 24">
<path d="M20.461 3.488c-2.245-2.25-5.231-3.488-8.414-3.488-6.553 0-11.887 5.334-11.887 11.892 0 2.095 0.548 4.144 1.589 5.944l-1.688 6.164 6.304-1.654c1.739 0.947 3.694 1.448 5.681 1.448h0.005c0 0 0 0 0 0 6.553 0 11.892-5.335 11.892-11.892 0-3.179-1.237-6.164-3.483-8.414zM12.052 21.788v0c-1.777 0-3.516-0.478-5.035-1.378l-0.361-0.216-3.74 0.98 0.998-3.647-0.235-0.375c-0.994-1.571-1.514-3.389-1.514-5.259 0-5.452 4.434-9.886 9.891-9.886 2.639 0 5.123 1.031 6.989 2.897 1.866 1.87 2.892 4.35 2.892 6.994-0.005 5.456-4.439 9.891-9.886 9.891zM17.471 14.381c-0.296-0.15-1.758-0.867-2.030-0.966s-0.469-0.15-0.67 0.15c-0.197 0.296-0.769 0.966-0.942 1.167-0.173 0.197-0.346 0.225-0.642 0.075s-1.256-0.464-2.391-1.477c-0.881-0.788-1.481-1.762-1.655-2.058s-0.019-0.46 0.131-0.605c0.136-0.131 0.295-0.347 0.445-0.52s0.197-0.296 0.296-0.497c0.098-0.197 0.052-0.37-0.023-0.521s-0.671-1.613-0.914-2.208c-0.239-0.581-0.488-0.502-0.671-0.511-0.173-0.009-0.37-0.009-0.567-0.009s-0.52 0.075-0.792 0.37c-0.272 0.296-1.040 1.017-1.040 2.479s1.064 2.873 1.214 3.075c0.15 0.197 2.096 3.202 5.077 4.486 0.708 0.305 1.261 0.488 1.692 0.628 0.712 0.225 1.36 0.192 1.87 0.117 0.572-0.085 1.758-0.717 2.006-1.411s0.248-1.289 0.173-1.411c-0.070-0.131-0.267-0.206-0.567-0.356z"></path>
</symbol>
<symbol id="apkm-icon-f-droid-checkmark" viewBox="0 0 24 24">
<path d="M12.111 21.368c-2.531 0-4.59-2.059-4.59-4.59s2.059-4.59 4.59-4.59c0.001 0 0.002 0 0.003 0 0.877 0 1.696 0.246 2.392 0.673l-0.020-0.011c0.151 0.090 0.25 0.253 0.25 0.439 0 0.282-0.228 0.51-0.51 0.51-0.099 0-0.192-0.029-0.271-0.078l0.002 0.001c-0.526-0.323-1.163-0.514-1.844-0.514-0.001 0-0.002 0-0.003 0h0c-1.969 0-3.57 1.602-3.57 3.57s1.601 3.57 3.57 3.57c1.968 0 3.57-1.601 3.57-3.57-0-0.16-0.010-0.318-0.030-0.473l0.002 0.018c-0.003-0.019-0.004-0.042-0.004-0.064 0-0.259 0.193-0.473 0.443-0.506l0.003-0c0.019-0.003 0.042-0.004 0.064-0.004 0.259 0 0.473 0.193 0.505 0.443l0 0.003c0.023 0.175 0.037 0.377 0.037 0.583v0c0 2.531-2.059 4.59-4.59 4.59zM11.919 18.592l-0.014-0c-0.146-0.004-0.275-0.069-0.365-0.169l-0-0-1.328-1.486c-0.081-0.090-0.13-0.209-0.13-0.34 0-0.282 0.228-0.51 0.51-0.51 0.151 0 0.286 0.066 0.38 0.17l0 0 0.968 1.083 3.197-3.197c0.092-0.090 0.217-0.145 0.356-0.145 0.282 0 0.51 0.228 0.51 0.51 0 0.139-0.055 0.264-0.145 0.356l-3.579 3.579c-0.092 0.092-0.22 0.149-0.36 0.149-0 0-0 0-0 0v0zM20.47 10.082h-16.943c-0.877 0-1.589 0.711-1.589 1.589v10.589c0 0.877 0.711 1.589 1.589 1.589h16.943c0.877 0 1.589-0.711 1.589-1.589v-10.589c0-0.878-0.712-1.589-1.589-1.589zM23.847 0.398c-0.001 0.001-0.002 0.002-0.002 0.003-0.002-0.002-0.004-0.003-0.006-0.005 0.001-0.001 0.002-0.003 0.004-0.004-0.116-0.137-0.279-0.231-0.519-0.238-0.202 0.005-0.391 0.097-0.512 0.259l-1.818 2.353c-0.164-0.058-0.339-0.095-0.523-0.095h-16.944c-0.184 0-0.358 0.038-0.523 0.095l-1.818-2.354c-0.121-0.162-0.31-0.253-0.512-0.259-0.24 0.006-0.403 0.1-0.519 0.238 0.001 0.001 0.002 0.003 0.004 0.004-0.003 0.002-0.005 0.004-0.007 0.006 0-0.001-0.001-0.002-0.002-0.003-0.066 0.078-0.297 0.426-0.012 0.824l1.909 2.471c-0.067 0.176-0.108 0.366-0.108 0.566v3.707c0 0.877 0.711 1.589 1.589 1.589h16.943c0.877 0 1.589-0.711 1.589-1.589v-3.707c0-0.2-0.041-0.39-0.109-0.566l1.909-2.471c0.284-0.398 0.053-0.746-0.013-0.824zM6.902 8.229c-0.987 0-1.787-0.8-1.787-1.787s0.8-1.787 1.787-1.787c0.987 0 1.787 0.8 1.787 1.787s-0.8 1.787-1.787 1.787zM17.227 8.229c-0.987 0-1.787-0.8-1.787-1.787s0.8-1.787 1.787-1.787c0.987 0 1.787 0.8 1.787 1.787s-0.8 1.787-1.787 1.787z"></path>
</symbol>
<symbol id="apkm-icon-f-droid" viewBox="0 0 24 24">
<path d="M20.472 10.081h-16.944c-0.877 0-1.589 0.711-1.589 1.589v10.59c0 0.877 0.711 1.589 1.589 1.589h16.944c0.877 0 1.589-0.711 1.589-1.589v-10.59c0-0.878-0.712-1.589-1.589-1.589zM12 22.525c-3.066 0-5.56-2.494-5.56-5.56s2.494-5.56 5.56-5.56 5.56 2.494 5.56 5.56-2.494 5.56-5.56 5.56zM12 12.411c-2.511 0-4.554 2.043-4.554 4.554s2.043 4.554 4.554 4.554 4.554-2.043 4.554-4.554-2.043-4.554-4.554-4.554zM12 20.274c-1.563 0-2.881-1.103-3.221-2.568h1.67c0.275 0.581 0.859 0.979 1.551 0.979 0.96 0 1.721-0.761 1.721-1.721s-0.761-1.721-1.721-1.721c-0.649 0-1.2 0.352-1.493 0.874h-1.702c0.378-1.412 1.669-2.462 3.195-2.462 1.818 0 3.309 1.491 3.309 3.309 0 1.819-1.491 3.31-3.309 3.31zM23.849 0.396c-0.001 0.001-0.002 0.002-0.002 0.003-0.002-0.002-0.004-0.003-0.006-0.005 0.001-0.001 0.002-0.003 0.004-0.004-0.116-0.137-0.279-0.231-0.519-0.238-0.202 0.005-0.391 0.097-0.512 0.259l-1.818 2.353c-0.164-0.058-0.339-0.095-0.523-0.095h-16.945c-0.184 0-0.358 0.038-0.523 0.095l-1.818-2.354c-0.121-0.162-0.31-0.253-0.512-0.259-0.24 0.006-0.403 0.1-0.519 0.238 0.001 0.001 0.002 0.003 0.004 0.004-0.003 0.002-0.005 0.004-0.007 0.006 0-0.001-0.001-0.002-0.002-0.003-0.066 0.078-0.297 0.426-0.012 0.824l1.909 2.471c-0.067 0.176-0.108 0.366-0.108 0.566v3.707c0 0.877 0.711 1.589 1.589 1.589h16.944c0.877 0 1.589-0.711 1.589-1.589v-3.707c0-0.2-0.041-0.39-0.109-0.566l1.909-2.471c0.284-0.398 0.053-0.746-0.013-0.824zM6.904 8.228c-0.987 0-1.787-0.8-1.787-1.787s0.8-1.787 1.787-1.787 1.787 0.8 1.787 1.787-0.8 1.787-1.787 1.787zM17.229 8.228c-0.987 0-1.787-0.8-1.787-1.787s0.8-1.787 1.787-1.787 1.787 0.8 1.787 1.787-0.8 1.787-1.787 1.787z"></path>
</symbol>
</defs>
</svg>
<div class="modal fade" id="uploadAPK" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true"><div class="modal-dialog"><div class="modal-content">    <div class="modal-header">
        <div class="h1 fontBlack modal-title">Submit an APK</div>    </div>
    <div class="modal-body">
        <p>Have an APK file for an alpha, beta, or staged rollout update? Just drop it below, fill in any details you know, and we'll do the rest!</p>
        <p>On Android, you can use <a href="https://play.google.com/store/apps/details?id=com.javiersantos.mlmanager" target="_blank" class="external" rel="nofollow">ML Manager</a>, which has built-in support for uploading to APKMirror.</p>
        <p class="modal-spacing"><span class="fontOrange">NOTE:</span> Every APK file is manually reviewed by the APKMirror team before being posted to the site.</p>
        <div id="error-message-bar" class="modal-spacing hidden"><span class="fontWarning">ERROR:</span> <span id="errorMessage"></span></div>
        <div id="success-message-bar" class="modal-spacing hidden"><span class="fontBlue">SUCCESS:</span> Your files have been uploaded, please check if there were any errors.</div>
        <input type="file" multiple="multiple" id="hidden-file-input" class="hidden">
        <div class="row d-flex f-a-start">
            <div class="f-100 f-sm-50 col-padding p-relative">
                <div class="progress progress-striped active upload-progress">
                    <div id="total-upload-progress" class="progress-bar" style="width: 0%"></div>
                </div>
                <div class="uploadAreaTable p-relative">

                    <div class="uploadArea" id="myDropZone">
                        <div class="uploadCloud dz-message">
                            <svg class="icon upload-large-icon">
                                <use xlink:href="#apkm-icon-upload"></use>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="f-100 f-sm-50 col-padding">
                <div class="group">
                    <div class="form-control-wrapper">
                        <input class="form-control" id="upload-apk-fullname" type="text" placeholder="Your name (shown publicly)">
                        <div class="floating-label">Your name (shown publicly)</div>
                    </div>
                </div>

                <div class="group">
                    <div class="form-control-wrapper">
                        <input class="form-control" id="upload-apk-email" type="email" placeholder="Your email (stored but not shown publicly)">
                        <div class="floating-label">Your email (stored but not shown publicly)</div>
                    </div>
                </div>
                <div class="group">
                    <div class="form-control-wrapper">
                        <textarea class="form-control" rows="11" id="upload-apk-changes" placeholder="What's new (may be shown publicly)"></textarea>
                        <div class="floating-label">What's new (may be shown publicly)</div>
                    </div>
                </div>
                                                    <button class="pull-right buttonUpload btn btn-default hidden" id="btnSubmit">Upload</button>
                    <button id="btnDismiss" class="pull-right btn btn-default hidden" data-dismiss="modal">Close</button>
                            </div>
        </div>
    </div>
    
</div></div></div><style>.tab-content .tab-pane [class*="ai-viewport"], .tab-content .tab-pane [class*="ai-viewport"] .adsbygoogle{display: block !important}</style>                                <div class="modal fade" id="languages" tabindex="-1" role="dialog" aria-labelledby="languages" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h2 class="modal-title fontBlack">Languages</h2>
                                            </div>
                                            <div class="modal-body">
                                                Afrikaans/Afrikaans (af), Amharic/አማርኛ (am), Angika/Angika (anp), Arabic/العربية (ar), Moroccan Arabic/Moroccan Arabic (ary), Assamese/অসমীয়া (as), Asturian/asturianu (ast), Avaric/Avaric (av), Azerbaijani/azərbaycan (az), azb, Bashkir/Bashkir (ba), Balinese/Balinese (ban), Belarusian/беларуская (be), Betawi/Betawi (bew), Bulgarian/български (bg), Bhojpuri/भोजपुरी (bho), blk, Bangla/বাংলা (bn), Breton/brezhoneg (br), Bosnian/bosanski (bs), btm, Catalan/català (ca), Chechen/нохчийн (ce), Central Kurdish/کوردیی ناوەندی (ckb), cnh, Czech/čeština (cs), Church Slavic/Church Slavic (cu), Chuvash/чӑваш (cv), Welsh/Cymraeg (cy), Danish/dansk (da), dag, German/Deutsch (de), dga, Zaza/Zaza (diq), Greek/Ελληνικά (el), English/English (en), Esperanto/esperanto (eo), Spanish/español (es), Estonian/eesti (et), Basque/euskara (eu), Persian/فارسی (fa), Finnish/suomi (fi), Filipino/Filipino (fil), Faroese/føroyskt (fo), French/français (fr), Western Frisian/Frysk (fy), Irish/Gaeilge (ga), Galician/galego (gl), Guarani/Guarani (gn), Gujarati/ગુજરાતી (gu), Hausa/Hausa (ha), Hebrew/עברית (he), Hindi/हिन्दी (hi), Croatian/hrvatski (hr), Upper Sorbian/hornjoserbšćina (hsb), Hungarian/magyar (hu), Armenian/հայերեն (hy), Interlingua/interlingua (ia), Indonesian/Indonesia (id), Indonesian/Indonesia (in), Ingush/Ingush (inh), Ido/Ido (io), Icelandic/íslenska (is), isv, Italian/italiano (it), Hebrew/עברית (iw), Japanese/日本語 (ja), Yiddish/Yiddish (ji), Javanese/Jawa (jv), Georgian/ქართული (ka), Kabyle/Taqbaylit (kab), Tyap/Tyap (kcg), Kazakh/қазақ тілі (kk), Khmer/ខ្មែរ (km), Kannada/ಕನ್ನಡ (kn), Korean/한국어 (ko), Karachay-Balkar/Karachay-Balkar (krc), Kashmiri/کٲشُر (ks), Colognian/Kölsch (ksh), Kurdish/kurdî (ku), Kumyk/Kumyk (kum), kus, Cornish/kernewek (kw), Kyrgyz/кыргызча (ky), Luxembourgish/Lëtzebuergesch (lb), Limburgish/Limburgish (li), Lombard/Lombard (lmo), Lao/ລາວ (lo), Lithuanian/lietuvių (lt), Latvian/latviešu (lv), Malagasy/Malagasy (mg), Mari/Mari (mhr), Minangkabau/Minangkabau (min), Macedonian/македонски (mk), Malayalam/മലയാളം (ml), Mongolian/монгол (mn), Manipuri/মৈতৈলোন্ (mni), mnw, Marathi/मराठी (mr), Malay/Melayu (ms), Maltese/Malti (mt), Burmese/မြန်မာ (my), Norwegian Bokmål/norsk bokmål (nb), Nepali/नेपाली (ne), Nias/Nias (nia), Dutch/Nederlands (nl), N’Ko/N’Ko (nqo), Occitan/Occitan (oc), olo, Oromo/Oromoo (om), Odia/ଓଡ଼ିଆ (or), Punjabi/ਪੰਜਾਬੀ (pa), Polish/polski (pl), Western Panjabi/Western Panjabi (pnb), Pashto/پښتو (ps), Portuguese/português (pt), qq, rki, Romanian/română (ro), Russian/русский (ru), Sanskrit/संस्कृत भाषा (sa), Yakut/саха тыла (sah), Sicilian/Sicilian (scn), Sindhi/سنڌي (sd), Sassarese Sardinian/Sassarese Sardinian (sdc), Southern Kurdish/Southern Kurdish (sdh), Northern Sami/davvisámegiella (se), Serbo-Croatian/srpskohrvatski (sh), Shan/Shan (shn), Sinhala/සිංහල (si), Slovak/slovenčina (sk), skr, Slovenian/slovenščina (sl), Inari Sami/anarâškielâ (smn), Skolt Sami/Skolt Sami (sms), Albanian/shqip (sq), Serbian/српски (sr), sro, Sundanese/Basa Sunda (su), Swedish/svenska (sv), Swahili/Kiswahili (sw), Tamil/தமிழ் (ta), Tulu/Tulu (tcy), Telugu/తెలుగు (te), Tajik/тоҷикӣ (tg), Thai/ไทย (th), Tigrinya/ትግርኛ (ti), Tagalog/Tagalog (tl), Talysh/Talysh (tly), Turkish/Türkçe (tr), Tatar/татар (tt), Ukrainian/українська (uk), Urdu/اردو (ur), Uzbek/o‘zbek (uz), Vietnamese/Tiếng Việt (vi), Mingrelian/Mingrelian (xmf), Nheengatu/nheẽgatu (yrl), Standard Moroccan Tamazight/ⵜⴰⵎⴰⵣⵉⵖⵜ (zgh), Chinese/中文 (zh), Zulu/isiZulu (zu)                                            </div>
                                            <div class="modal-footer">
                                                <button style="color: white;" type="button" class="accent_bg btn btn-flat" data-dismiss="modal">Got It!
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>                                     <div class="modal fade" id="apkPermissions" tabindex="-1" role="dialog" aria-labelledby="" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-body">
                                                                                                            <h2 class="modal-title fontBlack">Permissions</h2><br>
                                                    android.permission.ACCESS_COARSE_LOCATION<br>android.permission.ACCESS_FINE_LOCATION<br>android.permission.ACCESS_NETWORK_STATE<br>android.permission.ACCESS_WIFI_STATE<br>android.permission.AUTHENTICATE_ACCOUNTS<br>android.permission.FOREGROUND_SERVICE<br>android.permission.GET_ACCOUNTS<br>android.permission.INTERNET<br>android.permission.MANAGE_ACCOUNTS<br>android.permission.POST_NOTIFICATIONS<br>android.permission.READ_EXTERNAL_STORAGE<br>android.permission.RECEIVE_BOOT_COMPLETED<br>android.permission.VIBRATE<br>android.permission.WAKE_LOCK<br>android.permission.WRITE_EXTERNAL_STORAGE<br>com.google.android.c2dm.permission.RECEIVE<br>com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE<br>org.wikipedia.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION<br><br>                                                        <h2 class="modal-title fontBlack">Features</h2><br>
                                                    android.hardware.location<br>android.hardware.location.gps<br>android.hardware.touchscreen<br>android.hardware.wifi<br><br>                                                        <h2 class="modal-title fontBlack">Libraries</h2><br>
                                                    androidx.window.extensions<br>androidx.window.sidecar<br>com.sec.android.app.multiwindow<br>org.apache.http.legacy                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                        <style>
            .accent_bg.owV {
                color: #fff !important;
                background-color: #828282 !important;
                pointer-events: none;
                cursor: default;
            }

            .KJv:not(.hidden) .progress-bar {
                animation: width 15s linear;
            }
        </style>
                <script>
            function aOm() {
                APKM.qs('style:last-of-type').appendChild(document.createTextNode(' .NJx:empty {display: none !important;}'));
                if (APKM.vE('.GEo', 21) < 2) {
                    !function(){let s=APKM.qa(".pNa"),e=APKM.qs(".KJv"),o=APKM.qs(".IHl");APKM.rT(function(d){0==d?(s.forEach(d=>{d.classList.remove("owV")}),e.classList.add("hidden")):o.innerHTML=d+" more sec"},15),APKM.qa(".pHr").forEach(d=>{d.classList.remove("hidden")}),s.forEach(d=>{d.classList.add("owV")})}();;
                }
            }
        </script>
        <script>
        </script>
<script type="text/javascript" async="async" src="https://www.apkmirror.com/wp-content/plugins/AppManager/js/appmanager.js?ver=1749078259" id="appmanager_javascript-async-js"></script>
<script type="text/javascript" id="disqus_embed-js-extra">
/* <![CDATA[ */
var embedVars = {"disqusConfig":{"integration":"wordpress 3.1.2"},"disqusIdentifier":"9899167 https:\/\/www.apkmirror.com\/?post_type=apps_post&p=9899167","disqusShortname":"apkmirror","disqusTitle":"Wikipedia 2.7.50538-samsung-2025-06-24 (nodpi) (Android 5.0+)","disqusUrl":"https:\/\/apkmirror.com\/apk\/wikimedia-foundation\/wikipedia\/wikipedia-2-7-50538-release\/wikipedia-2-7-50538-samsung-2025-06-24-android-apk-download\/","postId":"9899167"};
/* ]]> */
</script>
<script type="text/javascript" data-src="https://www.apkmirror.com/wp-content/plugins/disqus-comment-system/public/js/comment_embed.js?ver=3.1.2" id="disqus_embed-js"></script>
<script type="text/javascript" id="jetpack-stats-js-before">
/* <![CDATA[ */
_stq = window._stq || [];
_stq.push([ "view", JSON.parse("{\"v\":\"ext\",\"blog\":\"165057178\",\"post\":\"9899167\",\"tz\":\"0\",\"srv\":\"apkmirror.com\",\"j\":\"1:14.5\"}") ]);
_stq.push([ "clickTrackerInit", "165057178", "9899167" ]);
/* ]]> */
</script>
<script type="text/javascript" src="https://stats.wp.com/e-202528.js" id="jetpack-stats-js" defer="defer" data-wp-strategy="defer"></script>
<script>
function b2a(a){var b,c=0,l=0,f="",g=[];if(!a)return a;do{var e=a.charCodeAt(c++);var h=a.charCodeAt(c++);var k=a.charCodeAt(c++);var d=e<<16|h<<8|k;e=63&d>>18;h=63&d>>12;k=63&d>>6;d&=63;g[l++]="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(e)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(h)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(k)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(d)}while(c<
a.length);return f=g.join(""),b=a.length%3,(b?f.slice(0,b-3):f)+"===".slice(b||3)}function a2b(a){var b,c,l,f={},g=0,e=0,h="",k=String.fromCharCode,d=a.length;for(b=0;64>b;b++)f["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(b)]=b;for(c=0;d>c;c++)for(b=f[a.charAt(c)],g=(g<<6)+b,e+=6;8<=e;)((l=255&g>>>(e-=8))||d-2>c)&&(h+=k(l));return h}b64e=function(a){return btoa(encodeURIComponent(a).replace(/%([0-9A-F]{2})/g,function(b,a){return String.fromCharCode("0x"+a)}))};
b64d=function(a){return decodeURIComponent(atob(a).split("").map(function(a){return"%"+("00"+a.charCodeAt(0).toString(16)).slice(-2)}).join(""))};
/* <![CDATA[ */
ai_front = {"insertion_before":"BEFORE","insertion_after":"AFTER","insertion_prepend":"PREPEND CONTENT","insertion_append":"APPEND CONTENT","insertion_replace_content":"REPLACE CONTENT","insertion_replace_element":"REPLACE ELEMENT","visible":"VISIBLE","hidden":"HIDDEN","fallback":"FALLBACK","automatically_placed":"Automatically placed by AdSense Auto ads code","cancel":"Cancel","use":"Use","add":"Add","parent":"Parent","cancel_element_selection":"Cancel element selection","select_parent_element":"Select parent element","css_selector":"CSS selector","use_current_selector":"Use current selector","element":"ELEMENT","path":"PATH","selector":"SELECTOR"};
/* ]]> */
var ai_cookie_js=!0,ai_block_class_def="ains";
/*
 js-cookie v3.0.5 | MIT  JavaScript Cookie v2.2.0
 https://github.com/js-cookie/js-cookie

 Copyright 2006, 2015 Klaus Hartl & Fagner Brack
 Released under the MIT license
*/
if("undefined"!==typeof ai_cookie_js){(function(a,f){"object"===typeof exports&&"undefined"!==typeof module?module.exports=f():"function"===typeof define&&define.amd?define(f):(a="undefined"!==typeof globalThis?globalThis:a||self,function(){var b=a.Cookies,c=a.Cookies=f();c.noConflict=function(){a.Cookies=b;return c}}())})(this,function(){function a(b){for(var c=1;c<arguments.length;c++){var g=arguments[c],e;for(e in g)b[e]=g[e]}return b}function f(b,c){function g(e,d,h){if("undefined"!==typeof document){h=
a({},c,h);"number"===typeof h.expires&&(h.expires=new Date(Date.now()+864E5*h.expires));h.expires&&(h.expires=h.expires.toUTCString());e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var l="",k;for(k in h)h[k]&&(l+="; "+k,!0!==h[k]&&(l+="="+h[k].split(";")[0]));return document.cookie=e+"="+b.write(d,e)+l}}return Object.create({set:g,get:function(e){if("undefined"!==typeof document&&(!arguments.length||e)){for(var d=document.cookie?document.cookie.split("; "):
[],h={},l=0;l<d.length;l++){var k=d[l].split("="),p=k.slice(1).join("=");try{var n=decodeURIComponent(k[0]);h[n]=b.read(p,n);if(e===n)break}catch(q){}}return e?h[e]:h}},remove:function(e,d){g(e,"",a({},d,{expires:-1}))},withAttributes:function(e){return f(this.converter,a({},this.attributes,e))},withConverter:function(e){return f(a({},this.converter,e),this.attributes)}},{attributes:{value:Object.freeze(c)},converter:{value:Object.freeze(b)}})}return f({read:function(b){'"'===b[0]&&(b=b.slice(1,-1));
return b.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(b){return encodeURIComponent(b).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"})});AiCookies=Cookies.noConflict();function m(a){if(null==a)return a;'"'===a.charAt(0)&&(a=a.slice(1,-1));try{a=JSON.parse(a)}catch(f){}return a}ai_check_block=function(a){var f="undefined"!==typeof ai_debugging;if(null==a)return!0;var b=m(AiCookies.get("aiBLOCKS"));ai_debug_cookie_status="";null==b&&(b={});"undefined"!==
typeof ai_delay_showing_pageviews&&(b.hasOwnProperty(a)||(b[a]={}),b[a].hasOwnProperty("d")||(b[a].d=ai_delay_showing_pageviews,f&&console.log("AI CHECK block",a,"NO COOKIE DATA d, delayed for",ai_delay_showing_pageviews,"pageviews")));if(b.hasOwnProperty(a)){for(var c in b[a]){if("x"==c){var g="",e=document.querySelectorAll('span[data-ai-block="'+a+'"]')[0];"aiHash"in e.dataset&&(g=e.dataset.aiHash);e="";b[a].hasOwnProperty("h")&&(e=b[a].h);f&&console.log("AI CHECK block",a,"x cookie hash",e,"code hash",
g);var d=new Date;d=b[a][c]-Math.round(d.getTime()/1E3);if(0<d&&e==g)return ai_debug_cookie_status=b="closed for "+d+" s = "+Math.round(1E4*d/3600/24)/1E4+" days",f&&console.log("AI CHECK block",a,b),f&&console.log(""),!1;f&&console.log("AI CHECK block",a,"removing x");ai_set_cookie(a,"x","");b[a].hasOwnProperty("i")||b[a].hasOwnProperty("c")||ai_set_cookie(a,"h","")}else if("d"==c){if(0!=b[a][c])return ai_debug_cookie_status=b="delayed for "+b[a][c]+" pageviews",f&&console.log("AI CHECK block",a,
b),f&&console.log(""),!1}else if("i"==c){g="";e=document.querySelectorAll('span[data-ai-block="'+a+'"]')[0];"aiHash"in e.dataset&&(g=e.dataset.aiHash);e="";b[a].hasOwnProperty("h")&&(e=b[a].h);f&&console.log("AI CHECK block",a,"i cookie hash",e,"code hash",g);if(0==b[a][c]&&e==g)return ai_debug_cookie_status=b="max impressions reached",f&&console.log("AI CHECK block",a,b),f&&console.log(""),!1;if(0>b[a][c]&&e==g){d=new Date;d=-b[a][c]-Math.round(d.getTime()/1E3);if(0<d)return ai_debug_cookie_status=
b="max imp. reached ("+Math.round(1E4*d/24/3600)/1E4+" days = "+d+" s)",f&&console.log("AI CHECK block",a,b),f&&console.log(""),!1;f&&console.log("AI CHECK block",a,"removing i");ai_set_cookie(a,"i","");b[a].hasOwnProperty("c")||b[a].hasOwnProperty("x")||(f&&console.log("AI CHECK block",a,"cookie h removed"),ai_set_cookie(a,"h",""))}}if("ipt"==c&&0==b[a][c]&&(d=new Date,g=Math.round(d.getTime()/1E3),d=b[a].it-g,0<d))return ai_debug_cookie_status=b="max imp. per time reached ("+Math.round(1E4*d/24/
3600)/1E4+" days = "+d+" s)",f&&console.log("AI CHECK block",a,b),f&&console.log(""),!1;if("c"==c){g="";e=document.querySelectorAll('span[data-ai-block="'+a+'"]')[0];"aiHash"in e.dataset&&(g=e.dataset.aiHash);e="";b[a].hasOwnProperty("h")&&(e=b[a].h);f&&console.log("AI CHECK block",a,"c cookie hash",e,"code hash",g);if(0==b[a][c]&&e==g)return ai_debug_cookie_status=b="max clicks reached",f&&console.log("AI CHECK block",a,b),f&&console.log(""),!1;if(0>b[a][c]&&e==g){d=new Date;d=-b[a][c]-Math.round(d.getTime()/
1E3);if(0<d)return ai_debug_cookie_status=b="max clicks reached ("+Math.round(1E4*d/24/3600)/1E4+" days = "+d+" s)",f&&console.log("AI CHECK block",a,b),f&&console.log(""),!1;f&&console.log("AI CHECK block",a,"removing c");ai_set_cookie(a,"c","");b[a].hasOwnProperty("i")||b[a].hasOwnProperty("x")||(f&&console.log("AI CHECK block",a,"cookie h removed"),ai_set_cookie(a,"h",""))}}if("cpt"==c&&0==b[a][c]&&(d=new Date,g=Math.round(d.getTime()/1E3),d=b[a].ct-g,0<d))return ai_debug_cookie_status=b="max clicks per time reached ("+
Math.round(1E4*d/24/3600)/1E4+" days = "+d+" s)",f&&console.log("AI CHECK block",a,b),f&&console.log(""),!1}if(b.hasOwnProperty("G")&&b.G.hasOwnProperty("cpt")&&0==b.G.cpt&&(d=new Date,g=Math.round(d.getTime()/1E3),d=b.G.ct-g,0<d))return ai_debug_cookie_status=b="max global clicks per time reached ("+Math.round(1E4*d/24/3600)/1E4+" days = "+d+" s)",f&&console.log("AI CHECK GLOBAL",b),f&&console.log(""),!1}ai_debug_cookie_status="OK";f&&console.log("AI CHECK block",a,"OK");f&&console.log("");return!0};
ai_check_and_insert_block=function(a,f){var b="undefined"!==typeof ai_debugging;if(null==a)return!0;var c=document.getElementsByClassName(f);if(c.length){c=c[0];var g=c.closest("."+ai_block_class_def),e=ai_check_block(a);!e&&0!=parseInt(c.getAttribute("limits-fallback"))&&c.hasAttribute("data-fallback-code")&&(b&&console.log("AI CHECK FAILED, INSERTING FALLBACK BLOCK",c.getAttribute("limits-fallback")),c.setAttribute("data-code",c.getAttribute("data-fallback-code")),null!=g&&g.hasAttribute("data-ai")&&
c.hasAttribute("fallback-tracking")&&c.hasAttribute("fallback_level")&&g.setAttribute("data-ai-"+c.getAttribute("fallback_level"),c.getAttribute("fallback-tracking")),e=!0);c.removeAttribute("data-selector");e?(ai_insert_code(c),g&&(b=g.querySelectorAll(".ai-debug-block"),b.length&&(g.classList.remove("ai-list-block"),g.classList.remove("ai-list-block-ip"),g.classList.remove("ai-list-block-filter"),g.style.visibility="",g.classList.contains("ai-remove-position")&&(g.style.position="")))):(b=c.closest("div[data-ai]"),
null!=b&&"undefined"!=typeof b.getAttribute("data-ai")&&(e=JSON.parse(b64d(b.getAttribute("data-ai"))),"undefined"!==typeof e&&e.constructor===Array&&(e[1]="",b.setAttribute("data-ai",b64e(JSON.stringify(e))))),g&&(b=g.querySelectorAll(".ai-debug-block"),b.length&&(g.classList.remove("ai-list-block"),g.classList.remove("ai-list-block-ip"),g.classList.remove("ai-list-block-filter"),g.style.visibility="",g.classList.contains("ai-remove-position")&&(g.style.position=""))));c.classList.remove(f)}c=document.querySelectorAll("."+
f+"-dbg");g=0;for(b=c.length;g<b;g++)e=c[g],e.querySelector(".ai-status").textContent=ai_debug_cookie_status,e.querySelector(".ai-cookie-data").textContent=ai_get_cookie_text(a),e.classList.remove(f+"-dbg")};ai_load_cookie=function(){var a="undefined"!==typeof ai_debugging,f=m(AiCookies.get("aiBLOCKS"));null==f&&(f={},a&&console.log("AI COOKIE NOT PRESENT"));a&&console.log("AI COOKIE LOAD",f);return f};ai_set_cookie=function(a,f,b){var c="undefined"!==typeof ai_debugging;c&&console.log("AI COOKIE SET block:",
a,"property:",f,"value:",b);var g=ai_load_cookie();if(""===b){if(g.hasOwnProperty(a)){delete g[a][f];a:{f=g[a];for(e in f)if(f.hasOwnProperty(e)){var e=!1;break a}e=!0}e&&delete g[a]}}else g.hasOwnProperty(a)||(g[a]={}),g[a][f]=b;0===Object.keys(g).length&&g.constructor===Object?(AiCookies.remove("aiBLOCKS"),c&&console.log("AI COOKIE REMOVED")):AiCookies.set("aiBLOCKS",JSON.stringify(g),{expires:365,path:"/"});if(c)if(a=m(AiCookies.get("aiBLOCKS")),"undefined"!=typeof a){console.log("AI COOKIE NEW",
a);console.log("AI COOKIE DATA:");for(var d in a){for(var h in a[d])"x"==h?(c=new Date,c=a[d][h]-Math.round(c.getTime()/1E3),console.log("  BLOCK",d,"closed for",c,"s = ",Math.round(1E4*c/3600/24)/1E4,"days")):"d"==h?console.log("  BLOCK",d,"delayed for",a[d][h],"pageviews"):"e"==h?console.log("  BLOCK",d,"show every",a[d][h],"pageviews"):"i"==h?(e=a[d][h],0<=e?console.log("  BLOCK",d,a[d][h],"impressions until limit"):(c=new Date,c=-e-Math.round(c.getTime()/1E3),console.log("  BLOCK",d,"max impressions, closed for",
c,"s =",Math.round(1E4*c/3600/24)/1E4,"days"))):"ipt"==h?console.log("  BLOCK",d,a[d][h],"impressions until limit per time period"):"it"==h?(c=new Date,c=a[d][h]-Math.round(c.getTime()/1E3),console.log("  BLOCK",d,"impressions limit expiration in",c,"s =",Math.round(1E4*c/3600/24)/1E4,"days")):"c"==h?(e=a[d][h],0<=e?console.log("  BLOCK",d,e,"clicks until limit"):(c=new Date,c=-e-Math.round(c.getTime()/1E3),console.log("  BLOCK",d,"max clicks, closed for",c,"s =",Math.round(1E4*c/3600/24)/1E4,"days"))):
"cpt"==h?console.log("  BLOCK",d,a[d][h],"clicks until limit per time period"):"ct"==h?(c=new Date,c=a[d][h]-Math.round(c.getTime()/1E3),console.log("  BLOCK",d,"clicks limit expiration in ",c,"s =",Math.round(1E4*c/3600/24)/1E4,"days")):"h"==h?console.log("  BLOCK",d,"hash",a[d][h]):console.log("      ?:",d,":",h,a[d][h]);console.log("")}}else console.log("AI COOKIE NOT PRESENT");return g};ai_get_cookie_text=function(a){var f=m(AiCookies.get("aiBLOCKS"));null==f&&(f={});var b="";f.hasOwnProperty("G")&&
(b="G["+JSON.stringify(f.G).replace(/"/g,"").replace("{","").replace("}","")+"] ");var c="";f.hasOwnProperty(a)&&(c=JSON.stringify(f[a]).replace(/"/g,"").replace("{","").replace("}",""));return b+c}};
var ai_internal_tracking=1,ai_external_tracking=0,ai_external_tracking_category="Ad Inserter Pro",ai_external_tracking_action="[EVENT]",ai_external_tracking_label="[BLOCK_NUMBER] - [BLOCK_VERSION_NAME]",ai_external_tracking_username="",ai_track_pageviews=0,ai_advanced_click_detection=0,ai_viewport_widths=[993,481,0],ai_viewport_indexes=[1,5,6],ai_viewport_names_string="WyJzaG93T25EZXNrdG9wIiwic2hvd09uVGFibGV0Iiwic2hvd09uTW9iaWxlIl0=",ai_data_id="62df16e434",
ai_ajax_url="https://www.apkmirror.com/wordpress/wp-admin/admin-ajax.php",ai_debug_tracking=0,ai_adb_attribute='ZGF0YS1kYXRhLW1hc2s=';
if("undefined"!==typeof ai_internal_tracking){ai_viewport_names=JSON.parse(b64d(ai_viewport_names_string));function F(k,l){return(new RegExp("^"+l.split("*").map(r=>r.replace(/([.*+?^=!:${}()|\[\]\/\\])/g,"\\$1")).join(".*")+"$")).test(k)}function I(k,l,r,x){if(x){const B=y=>{y.target&&y.target.matches(x)&&r(y)};k.addEventListener(l,B);return B}k.addEventListener(l,r);return r}installIframeTracker=function(k,l){"function"==typeof k&&(k={blurCallback:k});if(null===k||!1===k)ai_iframeTracker.untrack(l);
else if("object"==typeof k)ai_iframeTracker.track(l,k);else throw Error("Wrong handler type (must be an object, or null|false to untrack)");return this};var ai_mouseoverHander=function(k,l){l.data={handler:k};ai_iframeTracker.mouseoverListener(l)},ai_mouseoutHander=function(k,l){l.data={handler:k};ai_iframeTracker.mouseoutListener(l)};ai_iframeTracker={focusRetriever:null,focusRetrieved:!1,handlersList:[],isIE8AndOlder:!1,init:function(){try{9>navigator.userAgent.match(RegExp("(msie) ([\\w.]+)","i"))[2]&&
(this.isIE8AndOlder=!0)}catch(l){}window.focus();window.addEventListener("blur",l=>{ai_iframeTracker.windowLoseFocus(l)});var k=document.createElement("div");k.style="position:fixed; top:0; left:0; overflow:hidden;";k.innerHTML='<input style="position:absolute; left:-300px;" type="text" value="" id="focus_retriever" readonly="true" /><label for="focus_retriever">&nbsp;</label>';document.querySelector("body").append(k);this.focusRetriever=document.querySelector("#focus_retriever");this.focusRetrieved=
!1;if(this.isIE8AndOlder){this.focusRetriever.blur(function(l){l.stopPropagation();l.preventDefault();ai_iframeTracker.windowLoseFocus(l)});document.querySelector("body").addEventListener("click",l=>{window.focus()});document.querySelector("form").addEventListener("click",l=>{l.stopPropagation()});try{I(document.querySelector("body"),"click",l=>{l.stopPropagation()},"form")}catch(l){console.log("[iframeTracker] error (exception: "+l.message+")")}}},track:function(k,l){l.target=k;ai_iframeTracker.handlersList.push(l);
k.addEventListener("mouseover",ai_mouseoverHander.bind(event,l),!1);k.addEventListener("mouseout",ai_mouseoutHander.bind(event,l),!1)},untrack:function(k){if("function"!=typeof Array.prototype.filter)console.log("Your browser doesn't support Array filter, untrack disabled");else{k.forEach((B,y)=>{B.removeEventListener("mouseover",ai_mouseoverHander,!1);B.removeEventListener("mouseout",ai_mouseoutHander,!1)});var l=function(B){return null===B?!1:!0},r;for(r in this.handlersList){for(var x in this.handlersList[r].target)-1!==
$.inArray(this.handlersList[r].target[x],k)&&(this.handlersList[r].target[x]=null);this.handlersList[r].target=this.handlersList[r].target.filter(l);0===this.handlersList[r].target.length&&(this.handlersList[r]=null)}this.handlersList=this.handlersList.filter(l)}},mouseoverListener:function(k){k.data.handler.over=!0;ai_iframeTracker.retrieveFocus();try{k.data.handler.overCallback(k.data.handler.target,k)}catch(l){}},mouseoutListener:function(k){k.data.handler.over=!1;ai_iframeTracker.retrieveFocus();
try{k.data.handler.outCallback(k.data.handler.target,k)}catch(l){}},retrieveFocus:function(){if(document.activeElement&&"IFRAME"===document.activeElement.tagName){var k=!0;if(document.activeElement.hasAttribute("id")&&"undefined"!==typeof ai_ignore_iframe_ids&&ai_ignore_iframe_ids.constructor===Array){var l=document.activeElement.id;ai_ignore_iframe_ids.forEach(function(x){F(l,x)&&(k=!1)})}if(k&&document.activeElement.hasAttribute("class")&&"undefined"!==typeof ai_ignore_iframe_classes&&ai_ignore_iframe_classes.constructor===
Array){var r=document.activeElement.className;ai_ignore_iframe_classes.forEach(function(x){F(r,x)&&(k=!1)})}k&&(ai_iframeTracker.focusRetriever.focus(),ai_iframeTracker.focusRetrieved=!0)}},windowLoseFocus:function(k){for(var l in this.handlersList)if(!0===this.handlersList[l].over)try{this.handlersList[l].blurCallback(k)}catch(r){}}};function G(k){"complete"===document.readyState||"loading"!==document.readyState&&!document.documentElement.doScroll?k():document.addEventListener("DOMContentLoaded",
k)}G(function(){ai_iframeTracker.init()});ai_tracking_finished=!1;G(function(){function k(b,m,n,f,d,a,c){b=b.replace("[EVENT]",m);b=b.replace("[BLOCK_NUMBER]",n);b=b.replace("[BLOCK_NAME]",f);b=b.replace("[BLOCK_COUNTER]",d);b=b.replace("[VERSION_NUMBER]",a);b=b.replace("[VERSION_NAME]",c);b=b.replace("[BLOCK_VERSION_NUMBER]",n+(0==a?"":" - "+a));b=b.replace("[BLOCK_VERSION_NAME]",f+(""==c?"":" - "+c));return b=b.replace("[WP_USERNAME]",ai_external_tracking_username)}function l(b,m,n,f,d,a,c){var h=
k(ai_external_tracking_category,b,m,n,f,d,a),g=k(ai_external_tracking_action,b,m,n,f,d,a),q=k(ai_external_tracking_label,b,m,n,f,d,a);if("function"!=typeof ai_external_tracking_event||0!=ai_external_tracking_event({event:b,block:m,block_name:n,block_counter:f,version:d,version_name:a},h,g,q,c))"function"==typeof window.ga&&(b="send","string"==typeof ai_ga_tracker_name?b=ai_ga_tracker_name+"."+b:(m=ga.getAll(),0!=m.length&&(m=m[0].get("name"),"t0"!=m&&(b=m+"."+b))),ga(b,"event",{eventCategory:h,eventAction:g,
eventLabel:q,nonInteraction:c})),"function"==typeof window.gtag&&gtag("event","impression",{event_category:h,event_action:g,event_label:q,non_interaction:c}),"function"==typeof window.__gaTracker&&__gaTracker("send","event",{eventCategory:h,eventAction:g,eventLabel:q,nonInteraction:c}),"object"==typeof window.dataLayer&&window.dataLayer.push({event:"tracking",eventCategory:h,eventAction:g,eventLabel:q}),"object"==typeof _gaq&&_gaq.push(["_trackEvent",h,g,q,void 0,c]),"object"==typeof _paq&&_paq.push(["trackEvent",
h,g,q])}function r(b,m){var n=b[0],f=b[1];if(Number.isInteger(f))if("undefined"==typeof ai_check_data&&"undefined"==typeof ai_check_data_timeout)ai_check_data_timeout=!0,setTimeout(function(){r(b,m)},2500);else{ai_cookie=ai_load_cookie();for(var d in ai_cookie)if(parseInt(n)==parseInt(d))for(var a in ai_cookie[d])if("c"==a){var c=ai_cookie[d][a];if(0<c)if(ai_set_cookie(d,"c",c-1),1==c){c=document.querySelector('span[data-ai-block="'+n+'"]').dataset.aiCfpTime;var h=new Date;h=Math.round(h.getTime()/
1E3);var g=h+604800;ai_set_cookie(d,"c",-g);setTimeout(function(){document.querySelectorAll('span[data-ai-block="'+n+'"]').forEach((e,p)=>{(e=e.closest("div[data-ai]"))&&e.remove()})},50)}else ai_set_cookie(d,"c",c-1)}else"cpt"==a&&(c=ai_cookie[d][a],0<c?(ai_set_cookie(d,"cpt",c-1),1==c&&(c=document.querySelector('span[data-ai-block="'+n+'"]').dataset.aiCfpTime,h=new Date,h=Math.round(h.getTime()/1E3),g=ai_cookie[d].ct,ai_set_cookie(d,"x",g),setTimeout(function(){document.querySelectorAll('span[data-ai-block="'+
n+'"]').forEach((e,p)=>{(e=e.closest("div[data-ai]"))&&e.remove()})},75),"undefined"!=typeof c&&(g=h+86400*c,ai_set_cookie(n,"x",g),document.querySelectorAll("span.ai-cfp").forEach((e,p)=>{p=e.dataset.aiBlock;setTimeout(function(){var v=e.closest("div[data-ai]");v&&v.remove()},50);ai_set_cookie(p,"x",g)})))):ai_check_data.hasOwnProperty(d)&&ai_check_data[d].hasOwnProperty("cpt")&&ai_check_data[d].hasOwnProperty("ct")?ai_cookie.hasOwnProperty(d)&&ai_cookie[d].hasOwnProperty("ct")&&(h=new Date,c=ai_cookie[d].ct-
Math.round(h.getTime()/1E3),0>=c&&(h=Math.round(h.getTime()/1E3),ai_set_cookie(d,"cpt",ai_check_data[d].cpt-1),ai_set_cookie(d,"ct",Math.round(h+86400*ai_check_data[d].ct)))):(ai_cookie.hasOwnProperty(d)&&ai_cookie[d].hasOwnProperty("cpt")&&ai_set_cookie(d,"cpt",""),ai_cookie.hasOwnProperty(d)&&ai_cookie[d].hasOwnProperty("ct")&&ai_set_cookie(d,"ct","")));ai_cookie.hasOwnProperty("G")&&ai_cookie.G.hasOwnProperty("cpt")&&(c=ai_cookie.G.cpt,0<c?(ai_set_cookie("G","cpt",c-1),1==c&&(c=document.querySelector('span[data-ai-block="'+
n+'"]').dataset.aiCfpTime,h=new Date,h=Math.round(h.getTime()/1E3),g=ai_cookie.G.ct,ai_set_cookie(n,"x",g),setTimeout(function(){document.querySelectorAll('span[data-ai-block="'+n+'"]').forEach((e,p)=>{(e=e.closest("div[data-ai]"))&&e.remove()})},75),"undefined"!=typeof c&&(g=h+86400*c,ai_set_cookie(n,"x",g),document.querySelectorAll("span.ai-cfp").forEach((e,p)=>{p=e.dataset.aiBlock;setTimeout(function(){e.closest("div[data-ai]").remove()},50);ai_set_cookie(p,"x",g)})))):ai_check_data.hasOwnProperty("G")&&
ai_check_data.G.hasOwnProperty("cpt")&&ai_check_data.G.hasOwnProperty("ct")?ai_cookie.hasOwnProperty("G")&&ai_cookie.G.hasOwnProperty("ct")&&(h=new Date,c=ai_cookie.G.ct-Math.round(h.getTime()/1E3),0>=c&&(h=Math.round(h.getTime()/1E3),ai_set_cookie("G","cpt",ai_check_data.G.cpt-1),ai_set_cookie("G","ct",Math.round(h+86400*ai_check_data.G.ct)))):(ai_cookie.hasOwnProperty("G")&&ai_cookie.G.hasOwnProperty("cpt")&&ai_set_cookie("G","cpt",""),ai_cookie.hasOwnProperty("G")&&ai_cookie.G.hasOwnProperty("ct")&&
ai_set_cookie("G","ct","")));if(ai_internal_tracking&&"undefined"===typeof ai_internal_tracking_no_clicks){d={action:"ai_ajax",ai_check:ai_data_id,click:n,version:f,type:m};var q=[],u;for(u in d)a=encodeURIComponent(u),c=encodeURIComponent(d[u]),q.push(a+"="+c);q=q.join("&");(async function(){return await (await fetch(ai_ajax_url,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded; charset=UTF-8"},body:q})).text()})().then(e=>{e=e.trim();""!=e&&(e=JSON.parse(e),"undefined"!=
typeof e["#"]&&e["#"]==n&&(ai_cookie=ai_load_cookie(),e=Math.round((new Date).getTime()/1E3)+43200,ai_cookie.hasOwnProperty(n)&&ai_cookie[n].hasOwnProperty("x")||ai_set_cookie(n,"x",e),setTimeout(function(){document.querySelectorAll('span[data-ai-block="'+n+'"]').forEach((p,v)=>{(p=p.closest("div[data-ai]"))&&p.remove()})},50)))})}if(ai_external_tracking&&"undefined"===typeof ai_external_tracking_no_clicks){var t=b[2],w=b[3];l("click",n,t,b[4],f,w,!1)}"function"==typeof ai_click_action&&ai_click_action(n,
t,f,w)}}function x(){ai_check_data={};if("undefined"==typeof ai_iframe){ai_cookie=ai_load_cookie();document.querySelectorAll(".ai-check-block").forEach((f,d)=>{var a=f.dataset.aiBlock,c=f.dataset.aiDelayPv,h=f.dataset.aiEveryPv,g=f.dataset.aiHash,q=f.dataset.aiMaxImp,u=f.dataset.aiLimitImpPerTime,t=f.dataset.aiLimitImpTime,w=f.dataset.aiMaxClicks,e=f.dataset.aiLimitClicksPerTime,p=f.dataset.aiLimitClicksTime;d=f.dataset.aiGlobalLimitClicksPerTime;f=f.dataset.aiGlobalLimitClicksTime;if("undefined"!=
typeof c&&0<c){ai_check_data.hasOwnProperty(a)||(ai_check_data[a]={});ai_check_data[a].d=c;var v="";ai_cookie.hasOwnProperty(a)&&ai_cookie[a].hasOwnProperty("d")&&(v=ai_cookie[a].d);""===v&&ai_set_cookie(a,"d",c-1)}"undefined"!=typeof h&&2<=h&&(ai_check_data.hasOwnProperty(a)||(ai_check_data[a]={}),"undefined"!==typeof ai_delay_showing_pageviews||ai_cookie.hasOwnProperty(a)&&ai_cookie[a].hasOwnProperty("d")||(ai_cookie.hasOwnProperty(a)||(ai_cookie[a]={}),ai_cookie[a].d=0),ai_check_data[a].e=h);if("undefined"!=
typeof q&&0<q){if(ai_check_data.hasOwnProperty(a)||(ai_check_data[a]={}),ai_check_data[a].i=q,ai_check_data[a].h=g,h=c="",ai_cookie.hasOwnProperty(a)&&(ai_cookie[a].hasOwnProperty("i")&&(h=ai_cookie[a].i),ai_cookie[a].hasOwnProperty("h")&&(c=ai_cookie[a].h)),""===h||c!=g)ai_set_cookie(a,"i",q),ai_set_cookie(a,"h",g)}else ai_cookie.hasOwnProperty(a)&&ai_cookie[a].hasOwnProperty("i")&&(ai_set_cookie(a,"i",""),ai_cookie[a].hasOwnProperty("c")||ai_cookie[a].hasOwnProperty("x")||ai_set_cookie(a,"h",""));
if("undefined"!=typeof u&&0<u&&"undefined"!=typeof t&&0<t){ai_check_data.hasOwnProperty(a)||(ai_check_data[a]={});ai_check_data[a].ipt=u;ai_check_data[a].it=t;q=c="";ai_cookie.hasOwnProperty(a)&&(ai_cookie[a].hasOwnProperty("ipt")&&(c=ai_cookie[a].ipt),ai_cookie[a].hasOwnProperty("it")&&(q=ai_cookie[a].it));if(""===c||""===q)ai_set_cookie(a,"ipt",u),c=new Date,c=Math.round(c.getTime()/1E3),ai_set_cookie(a,"it",Math.round(c+86400*t));0<q&&(c=new Date,c=Math.round(c.getTime()/1E3),q<=c&&(ai_set_cookie(a,
"ipt",u),ai_set_cookie(a,"it",Math.round(c+86400*t))))}else ai_cookie.hasOwnProperty(a)&&(ai_cookie[a].hasOwnProperty("ipt")&&ai_set_cookie(a,"ipt",""),ai_cookie[a].hasOwnProperty("it")&&ai_set_cookie(a,"it",""));if("undefined"!=typeof w&&0<w){if(ai_check_data.hasOwnProperty(a)||(ai_check_data[a]={}),ai_check_data[a].c=w,ai_check_data[a].h=g,u=c="",ai_cookie.hasOwnProperty(a)&&(ai_cookie[a].hasOwnProperty("c")&&(u=ai_cookie[a].c),ai_cookie[a].hasOwnProperty("h")&&(c=ai_cookie[a].h)),""===u||c!=g)ai_set_cookie(a,
"c",w),ai_set_cookie(a,"h",g)}else ai_cookie.hasOwnProperty(a)&&ai_cookie[a].hasOwnProperty("c")&&(ai_set_cookie(a,"c",""),ai_cookie[a].hasOwnProperty("i")||ai_cookie[a].hasOwnProperty("x")||ai_set_cookie(a,"h",""));if("undefined"!=typeof e&&0<e&&"undefined"!=typeof p&&0<p){ai_check_data.hasOwnProperty(a)||(ai_check_data[a]={});ai_check_data[a].cpt=e;ai_check_data[a].ct=p;g=w="";ai_cookie.hasOwnProperty(a)&&(ai_cookie[a].hasOwnProperty("cpt")&&(w=ai_cookie[a].cpt),ai_cookie[a].hasOwnProperty("ct")&&
(g=ai_cookie[a].ct));if(""===w||""===g)ai_set_cookie(a,"cpt",e),c=new Date,c=Math.round(c.getTime()/1E3),ai_set_cookie(a,"ct",Math.round(c+86400*p));0<g&&(c=new Date,c=Math.round(c.getTime()/1E3),g<=c&&(ai_set_cookie(a,"cpt",e),ai_set_cookie(a,"ct",Math.round(c+86400*p))))}else ai_cookie.hasOwnProperty(a)&&(ai_cookie[a].hasOwnProperty("cpt")&&ai_set_cookie(a,"cpt",""),ai_cookie[a].hasOwnProperty("ct")&&ai_set_cookie(a,"ct",""));if("undefined"!=typeof d&&0<d&&"undefined"!=typeof f&&0<f){ai_check_data.hasOwnProperty("G")||
(ai_check_data.G={});ai_check_data.G.cpt=d;ai_check_data.G.ct=f;a=e="";ai_cookie.hasOwnProperty("G")&&(ai_cookie.G.hasOwnProperty("cpt")&&(e=ai_cookie.G.cpt),ai_cookie.G.hasOwnProperty("ct")&&(a=ai_cookie.G.ct));if(""===e||""===a)ai_set_cookie("G","cpt",d),c=new Date,c=Math.round(c.getTime()/1E3),ai_set_cookie("G","ct",Math.round(c+86400*f));0<a&&(c=new Date,c=Math.round(c.getTime()/1E3),a<=c&&(ai_set_cookie("G","cpt",d),ai_set_cookie("G","ct",Math.round(c+86400*f))))}else ai_cookie.hasOwnProperty("G")&&
(ai_cookie.G.hasOwnProperty("cpt")&&ai_set_cookie("G","cpt",""),ai_cookie.G.hasOwnProperty("ct")&&ai_set_cookie("G","ct",""))});document.querySelectorAll(".ai-check-block").forEach((f,d)=>{f.classList.remove("ai-check-block")});for(var b in ai_cookie)for(var m in ai_cookie[b])if("d"==m){var n=ai_cookie[b][m];0<n?ai_set_cookie(b,"d",n-1):ai_check_data.hasOwnProperty(b)&&ai_check_data[b].hasOwnProperty("e")?ai_set_cookie(b,"d",ai_check_data[b].e-1):ai_check_data.hasOwnProperty(b)&&ai_check_data[b].hasOwnProperty("d")||
ai_set_cookie(b,"d","")}}}function B(){if(ai_track_pageviews){var b=document.documentElement.clientWidth,m=window.innerWidth,n=b<m?m:b,f=0;ai_viewport_widths.every((h,g)=>n>=h?(f=ai_viewport_indexes[g],!1):!0);b=document.querySelector(b64d("Ym9keQ==")).getAttribute(b64d(ai_adb_attribute));if("string"===typeof b)var d=b==b64d("bWFzaw==");"string"===typeof b&&"boolean"===typeof d&&d&&(ai_external_tracking&&l("ad blocking",0,ai_viewport_names[f-1],0,0,"",!0),f|=128);y=[0,f]}x();ai_process_impressions();
if(0!=y.length&&ai_internal_tracking){d={action:"ai_ajax",ai_check:ai_data_id};var a=[],c;for(c in d)b=encodeURIComponent(c),m=encodeURIComponent(d[c]),a.push(b+"="+m);b=encodeURIComponent("views[]");m=encodeURIComponent(0);a.push(b+"="+m);b=encodeURIComponent("versions[]");m=encodeURIComponent(f);a.push(b+"="+m);a=a.join("&");(async function(){return await (await fetch(ai_ajax_url,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded; charset=UTF-8"},body:a})).text()})().then(h=>
{})}ai_tracking_finished=!0}ai_debug_tracking&&(ai_ajax_url+="?ai-debug-tracking=1");Number.isInteger=Number.isInteger||function(b){return"number"===typeof b&&isFinite(b)&&Math.floor(b)===b};ai_install_standard_click_trackers=function(b){"undefined"==typeof b&&(b=document.querySelector("body"));b=b.querySelectorAll("div.ai-track[data-ai]");var m=[];b.forEach((f,d)=>{if(f.offsetWidth||f.offsetHeight||f.getClientRects().length)f.querySelectorAll("div.ai-lazy, div.ai-wait-for-interaction, div.ai-manual, div.ai-list-manual, div.ai-manual-auto, div.ai-delayed").length||
m.push(f)});var n=[];m.forEach((f,d)=>{f.classList.remove("ai-track");n.push.apply(n,f.querySelectorAll("a"))});b=n;0!=b.length&&(ai_advanced_click_detection?b.forEach((f,d)=>{f.addEventListener("click",()=>{for(var a=f.closest("div[data-ai]");null!==a&&a.hasAttribute("data-ai");){var c=JSON.parse(b64d(a.getAttribute("data-ai")));"undefined"!==typeof c&&c.constructor===Array&&Number.isInteger(c[1])&&!a.classList.contains("clicked")&&(a.classList.add("clicked"),r(c,"a.click"));a=a.parentElement.closest("div[data-ai]")}})}):
b.forEach((f,d)=>{f.addEventListener("click",()=>{for(var a=f.closest("div[data-ai]");null!==a&&a.hasAttribute("data-ai");){var c=JSON.parse(b64d(a.getAttribute("data-ai")));"undefined"!==typeof c&&c.constructor===Array&&Number.isInteger(c[1])&&(r(c,"a.click"),clicked=!0);a=a.parentElement.closest("div[data-ai]")}})}))};ai_install_click_trackers=function(b){"undefined"==typeof b&&(b=document.querySelector("body"));if(ai_advanced_click_detection){var m=b.querySelectorAll("div.ai-track[data-ai], div.ai-rotate[data-info] div.ai-track[data-ai]"),
n=[];m.forEach((d,a)=>{(d.offsetWidth||d.offsetHeight||d.getClientRects().length)&&n.push(d)});b.hasAttribute("data-ai")&&b.classList.contains("ai-track")&&(b.offsetWidth||b.offsetHeight||b.getClientRects().length)&&n.push(b);var f=[];n.forEach((d,a)=>{d.querySelectorAll("div.ai-lazy, div.ai-wait-for-interaction, div.ai-manual, div.ai-list-manual, div.ai-manual-auto, div.ai-delayed").length||f.push(d)});m=f;0!=m.length&&m.forEach((d,a)=>{installIframeTracker({blurCallback:function(){if(null!=this.ai_data&&
null!=wrapper&&!wrapper.classList.contains("clicked")){wrapper.classList.add("clicked");r(this.ai_data,"blurCallback");for(var c=wrapper.querySelector("div[data-ai]");null!=c&&(c.offsetWidth||c.offsetHeight||c.getClientRects().length)&&c.hasAttribute("data-ai");){var h=JSON.parse(b64d(c.getAttribute("data-ai")));"undefined"!==typeof h&&h.constructor===Array&&Number.isInteger(h[1])&&r(h,"blurCallback INNER");c=c.querySelector("div[data-ai]")}}},overCallback:function(c){c=c.closest("div[data-ai]");
if(c.hasAttribute("data-ai")){var h=JSON.parse(b64d(c.getAttribute("data-ai")));"undefined"!==typeof h&&h.constructor===Array&&Number.isInteger(h[1])?(wrapper=c,this.ai_data=h):(null!=wrapper&&wrapper.classList.remove("clicked"),this.ai_data=wrapper=null)}},outCallback:function(c){null!=wrapper&&wrapper.classList.remove("clicked");this.ai_data=wrapper=null},focusCallback:function(c){if(null!=this.ai_data&&null!=wrapper&&!wrapper.classList.contains("clicked"))for(wrapper.classList.add("clicked"),r(this.ai_data,
"focusCallback"),c=wrapper.querySelector("div[data-ai]");null!=c&&(c.offsetWidth||c.offsetHeight||c.getClientRects().length)&&c.hasAttribute("data-ai");){var h=JSON.parse(b64d(c.getAttribute("data-ai")));"undefined"!==typeof h&&h.constructor===Array&&Number.isInteger(h[1])&&r(h,"focusCallback INNER");c=c.querySelector("div[data-ai]")}},wrapper:null,ai_data:null,block:null,version:null},d)})}ai_install_standard_click_trackers(b)};var y=[];ai_process_impressions=function(b){"undefined"==typeof b&&(b=
document.querySelector("body"));var m=[],n=[],f=[],d=[],a=[];0!=y.length&&(m.push(y[0]),n.push(y[1]),f.push("Pageviews"),a.push(0),d.push(""));var c=b.querySelectorAll("div.ai-track[data-ai], div.ai-rotate[data-info] div.ai-track[data-ai]"),h=[];c.forEach((e,p)=>{(e.offsetWidth||e.offsetHeight||e.getClientRects().length)&&!e.classList.contains("ai-no-pageview")&&h.push(e)});null!==b&&b.hasAttribute("data-ai")&&b.classList.contains("ai-track")&&!b.classList.contains("ai-no-pageview")&&(b.offsetWidth||
b.offsetHeight||b.getClientRects().length)&&h.push(b);c=h;0!=c.length&&c.forEach((e,p)=>{if(e.hasAttribute("data-ai")){p="";for(var v=1;9>=v;v++)if(e.hasAttribute("data-ai-"+v))p=e.getAttribute("data-ai-"+v);else break;""!=p&&e.setAttribute("data-ai",p);p=JSON.parse(b64d(e.getAttribute("data-ai")));if("undefined"!==typeof p&&p.constructor===Array){v=0;var z=e.querySelectorAll("div.ai-rotate[data-info]");1==z.length&&(v=JSON.parse(b64d(z[0].dataset.info))[1]);if(Number.isInteger(p[0])&&0!=p[0]&&Number.isInteger(p[1])){z=
0;var C=e.classList.contains("ai-no-tracking"),A=document.querySelector(b64d("Ym9keQ==")).getAttribute(b64d(ai_adb_attribute));if("string"===typeof A)var D=A==b64d("bWFzaw==");if("string"===typeof A&&"boolean"===typeof D){var E=e.offsetHeight;A=e.querySelectorAll(".ai-attributes");A.length&&A.forEach((H,J)=>{E>=e.offsetHeight&&(E-=e.offsetHeight)});A=e.querySelectorAll(".ai-code");E=0;A.length&&A.forEach((H,J)=>{E+=H.offsetHeight});D&&0===E&&(z=128)}0!=e.querySelectorAll("div.ai-lazy, div.ai-wait-for-interaction, div.ai-manual, div.ai-list-manual, div.ai-manual-auto, div.ai-delayed").length&&
(C=!0);if(!C)if(0==v)m.push(p[0]),n.push(p[1]|z),f.push(p[2]),d.push(p[3]),a.push(p[4]);else for(D=1;D<=v;D++)m.push(p[0]),n.push(D|z),f.push(p[2]),d.push(p[3]),a.push(p[4])}}}});ai_cookie=ai_load_cookie();for(var g in ai_cookie)if(m.includes(parseInt(g)))for(var q in ai_cookie[g])"i"==q?(b=ai_cookie[g][q],0<b&&(1==b?(b=new Date,b=Math.round(b.getTime()/1E3)+604800,ai_set_cookie(g,"i",-b)):ai_set_cookie(g,"i",b-1))):"ipt"==q&&(b=ai_cookie[g][q],0<b?ai_set_cookie(g,"ipt",b-1):ai_check_data.hasOwnProperty(g)&&
ai_check_data[g].hasOwnProperty("ipt")&&ai_check_data[g].hasOwnProperty("it")?ai_cookie.hasOwnProperty(g)&&ai_cookie[g].hasOwnProperty("it")&&(b=new Date,0>=ai_cookie[g].it-Math.round(b.getTime()/1E3)&&(b=Math.round(b.getTime()/1E3),ai_set_cookie(g,"ipt",ai_check_data[g].ipt),ai_set_cookie(g,"it",Math.round(b+86400*ai_check_data[g].it)))):(ai_cookie.hasOwnProperty(g)&&ai_cookie[g].hasOwnProperty("ipt")&&ai_set_cookie(g,"ipt",""),ai_cookie.hasOwnProperty(g)&&ai_cookie[g].hasOwnProperty("it")&&ai_set_cookie(g,
"it","")));if(m.length){if(ai_internal_tracking&&"undefined"===typeof ai_internal_tracking_no_impressions){y=[];g={action:"ai_ajax",ai_check:ai_data_id};var u=[],t;for(t in g)q=encodeURIComponent(t),b=encodeURIComponent(g[t]),u.push(q+"="+b);for(var w in m)q=encodeURIComponent("views[]"),b=encodeURIComponent(m[w]),u.push(q+"="+b);for(w in n)q=encodeURIComponent("versions[]"),b=encodeURIComponent(n[w]),u.push(q+"="+b);u=u.join("&");(async function(){return await (await fetch(ai_ajax_url,{method:"POST",
headers:{"Content-Type":"application/x-www-form-urlencoded; charset=UTF-8"},body:u})).text()})().then(e=>{e=e.trim();if(""!=e&&(e=JSON.parse(e),"undefined"!=typeof e["#"])){ai_cookie=ai_load_cookie();var p=Math.round((new Date).getTime()/1E3)+43200,v=[],z;for(z in e["#"])ai_cookie.hasOwnProperty(e["#"][z])&&ai_cookie[e["#"][z]].hasOwnProperty("x")||ai_set_cookie(e["#"][z],"x",p);setTimeout(function(){for(w=0;w<v.length;++w)document.querySelectorAll('span[data-ai-block="'+v[w]+'"]').forEach((C,A)=>
{(C=C.closest("div[data-ai]"))&&C.remove()})},50)}})}if(ai_external_tracking&&"undefined"===typeof ai_external_tracking_no_impressions)for(t=0;t<m.length;t++)0!=m[t]&&l("impression",m[t],f[t],a[t],n[t],d[t],!0)}};window.addEventListener("load",b=>{"undefined"==typeof ai_delay_tracking&&(ai_delay_tracking=0);setTimeout(B,ai_delay_tracking+1400);setTimeout(ai_install_click_trackers,ai_delay_tracking+1500)})})};

ai_js_code = true;
</script>
  
<script>
    if (typeof aOm !== 'function') {
        function aOm() {
        }
    }
    const thk = () => {        window.addEventListener('load', (event) => {
            aOm()        });
        }
    thk();</script>
          <div id="bottom-slider" class="apkm-timed-slider container-fluid set-max-width hidden" data-ttl="1209600">
            <a class="f-100 p-relative d-flex f-nowrap" href="/premium?utm_source=apkmirror&utm_medium=banner&utm_campaign=site_promo&utm_content=No+ads%2C+dark+mode%2C+and+more+with+APKMirror+Premium" data-apkm-click-track="premium page button" data-apkm-track-category="Bottom slider" data-google-interstitial="false">
                <span class="f-grow">
                    <span>No ads, dark mode, and more with APKMirror Premium</span>
                    <span class="more-btn">$10/quarter</span>
                </span>
                <span class="close f-stretch d-flex" aria-label="Close" data-apkm-click-track="close" data-apkm-track-category="Bottom slider"><span aria-hidden="true">&times;</span></span>
            </a>
        </div>
        <!-- Datonics -->
    <script type="text/javascript" src="//ads.pro-market.net/ads/scripts/site-140226.js" defer></script>
    <!-- End Datonics -->

    <!-- Cross Pixel -->
    <script type="text/javascript"> try{(function(){ var cb = new Date().getTime(); var s = document.createElement("script"); s.defer = true; s.src = "//tag.crsspxl.com/s1.js?d=2176&cb="+cb; var s0 = document.getElementsByTagName('script')[0]; s0.parentNode.insertBefore(s, s0); })();}catch(e){} </script>
    <!-- End Cross Pixel -->

    <!-- Quantcast Tag -->
    <script type="text/javascript">
        window._qevents = window._qevents || [];

        (function() {
            var elem = document.createElement('script');
            elem.src = (document.location.protocol == "https:" ? "https://secure" : "http://edge") + ".quantserve.com/quant.js";
            elem.async = true;
            elem.type = "text/javascript";
            var scpt = document.getElementsByTagName('script')[0];
            scpt.parentNode.insertBefore(elem, scpt);
        })();

        window._qevents.push({
            qacct:"p-95hmrIiT9zWMo",
            uid:"__INSERT_EMAIL_HERE__"
        });
    </script>

    <noscript>
        <div style="display:none;">
            <img src="//pixel.quantserve.com/pixel/p-95hmrIiT9zWMo.gif" border="0" height="1" width="1" alt="Quantcast"/>
        </div>
    </noscript>
    <!-- End Quantcast tag -->

    <!-- Begin comScore Tag -->
    <script>
    var _comscore = _comscore || [];
    _comscore.push({ c1: "2", c2: "15235859" });
    (function() {
        var s = document.createElement("script"), el = document.getElementsByTagName("script")[0]; s.async = true;
        s.src = (document.location.protocol == "https:" ? "https://sb" : "http://b") + ".scorecardresearch.com/beacon.js";
        el.parentNode.insertBefore(s, el);
    })();
    </script>
    <noscript>
        <div style="display:none;">
            <img src="https://b.scorecardresearch.com/p?c1=2&c2=15235859&cv=2.0&cj=1" border="0" height="1" width="1" alt="comScore"/>
        </div>
    </noscript>
    <!-- End comScore Tag -->

<!-- Begin instant.page -->
<script src="//instant.page/5.2.0" type="module" defer integrity="sha384-jnZyxPjiipYXnSU0ygqeac2q7CVYMbh84q0uHVRRxEtvFPiQYbXWUorga2aqZJ0z"></script>
<!-- End instant.page -->

</body>
</html>
