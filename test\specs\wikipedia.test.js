const { expect } = require('chai');

describe('Wikipedia App Test', () => {
    
    it('should launch Wikipedia app and perform search interaction', async () => {
        console.log('Test started: Wikipedia app search functionality');
        
        // Wait for app to load
        await browser.pause(3000);
        
        // Take initial screenshot
        await browser.saveScreenshot('./screenshots/01_app_launched.png');
        console.log('Screenshot taken: App launched');
        
        // Look for skip button and tap it if present (onboarding)
        try {
            const skipButton = await $('//android.widget.Button[@text="Skip"]');
            if (await skipButton.isDisplayed()) {
                await skipButton.click();
                console.log('Skipped onboarding');
                await browser.pause(1000);
            }
        } catch (error) {
            console.log('No skip button found or already skipped');
        }
        
        // Look for search input field - try multiple selectors
        let searchInput;
        const searchSelectors = [
            '//android.widget.EditText',
            '//*[@resource-id="org.wikipedia:id/search_src_text"]',
            '//*[@content-desc="Search Wikipedia"]',
            '//*[contains(@text, "Search")]',
            '//android.widget.TextView[contains(@text, "Search")]'
        ];
        
        for (const selector of searchSelectors) {
            try {
                searchInput = await $(selector);
                if (await searchInput.isDisplayed()) {
                    console.log(`Found search element with selector: ${selector}`);
                    break;
                }
            } catch (error) {
                console.log(`Selector ${selector} not found, trying next...`);
            }
        }
        
        // If no search input found, try tapping on search area
        if (!searchInput || !(await searchInput.isDisplayed())) {
            console.log('Trying to find search by tapping on search area');
            try {
                // Try to find and tap search container or icon
                const searchContainer = await $('//*[contains(@resource-id, "search")]');
                if (await searchContainer.isDisplayed()) {
                    await searchContainer.click();
                    await browser.pause(1000);
                    
                    // Try to find search input again after tapping
                    searchInput = await $('//android.widget.EditText');
                }
            } catch (error) {
                console.log('Could not find search container');
            }
        }
        
        // Verify search input is available
        expect(await searchInput.isDisplayed()).to.be.true;
        console.log('Search input field found and displayed');
        
        // Take screenshot before search
        await browser.saveScreenshot('./screenshots/02_before_search.png');
        
        // Perform search interaction
        const searchTerm = 'JavaScript';
        await searchInput.setValue(searchTerm);
        console.log(`Entered search term: ${searchTerm}`);
        
        // Wait for search suggestions to appear
        await browser.pause(2000);
        
        // Take screenshot after typing
        await browser.saveScreenshot('./screenshots/03_search_typed.png');
        
        // Look for search suggestions or results
        try {
            const searchSuggestions = await $$('//*[contains(@resource-id, "page_list_item")]');
            if (searchSuggestions.length > 0) {
                console.log(`Found ${searchSuggestions.length} search suggestions`);
                
                // Tap on first suggestion
                await searchSuggestions[0].click();
                console.log('Tapped on first search suggestion');
                
                // Wait for page to load
                await browser.pause(3000);
                
                // Take screenshot after selecting suggestion
                await browser.saveScreenshot('./screenshots/04_article_loaded.png');
                
                // Verify article content is displayed
                const articleContent = await $('//*[contains(@resource-id, "content")]');
                expect(await articleContent.isDisplayed()).to.be.true;
                console.log('Article content verified as displayed');
                
            } else {
                // If no suggestions, press enter to search
                await browser.keys('Enter');
                await browser.pause(3000);
                
                // Take screenshot after search
                await browser.saveScreenshot('./screenshots/04_search_results.png');
                
                // Verify search results are displayed
                const searchResults = await $('//*[contains(@text, "JavaScript") or contains(@text, "javascript")]');
                expect(await searchResults.isDisplayed()).to.be.true;
                console.log('Search results verified as displayed');
            }
        } catch (error) {
            console.log('Error during search interaction:', error.message);
            // Take screenshot of current state for debugging
            await browser.saveScreenshot('./screenshots/04_error_state.png');
            throw error;
        }
        
        // Final screenshot
        await browser.saveScreenshot('./screenshots/05_test_completed.png');
        console.log('Test completed successfully');
    });
    
    after(async () => {
        // Clean up - close app
        await browser.terminateApp('org.wikipedia');
        console.log('App terminated');
    });
});
