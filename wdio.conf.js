const path = require('path');

exports.config = {
    // Test runner configuration
    runner: 'local',

    // Specify test files
    specs: [
        './test/specs/**/*.js'
    ],

    // Patterns to exclude
    exclude: [],

    // Maximum instances to run
    maxInstances: 1,

    // WebDriver connection configuration
    hostname: 'localhost',
    port: 4723,
    path: '/',

    // Capabilities for Android testing
    capabilities: [{
        platformName: 'Android',
        'appium:deviceName': 'emulator-5554',
        'appium:platformVersion': '11.0', // Adjust based on your emulator
        'appium:automationName': 'UiAutomator2',
        'appium:app': path.join(process.cwd(), 'org-wikipedia.apk'),
        'appium:appPackage': 'org.wikipedia',
        'appium:appActivity': 'org.wikipedia.main.MainActivity',
        'appium:noReset': false,
        'appium:fullReset': true,
        'appium:newCommandTimeout': 240,
        'appium:androidInstallTimeout': 90000
    }],

    // Test framework
    framework: 'mocha',

    // Test reporter
    reporters: ['spec'],

    // Mocha options
    mochaOpts: {
        ui: 'bdd',
        timeout: 60000
    },

    // Services
    services: [],

    // Hooks
    before: function (capabilities, specs) {
        // Set implicit wait
        browser.setTimeout({
            'implicit': 10000
        });
    },

    beforeTest: function (test, context) {
        console.log(`Starting test: ${test.title}`);
    },

    afterTest: function (test, context, { error, result, duration, passed, retries }) {
        if (error) {
            console.log(`Test failed: ${test.title}`);
            // Take screenshot on failure
            browser.saveScreenshot(`./screenshots/FAILED_${test.title.replace(/\s+/g, '_')}_${Date.now()}.png`);
        }
    },

    after: function (result, capabilities, specs) {
        console.log('Test session completed');
    }
};
